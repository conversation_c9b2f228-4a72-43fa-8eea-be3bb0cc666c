{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Common/Components/Inputs/InputImageFileCustom.razor.js", "AssetFile": "Common/Components/Inputs/InputImageFileCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "122"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "Common/Components/Inputs/InputTextAreaCustom.razor.js", "AssetFile": "Common/Components/Inputs/InputTextAreaCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "Common/Components/Select/NewSelect2.razor.js", "AssetFile": "Common/Components/Select/NewSelect2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "Common/Components/Select/Select2.razor.js", "AssetFile": "Common/Components/Select/Select2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "Common/Components/Sortable/SortableList.razor.js", "AssetFile": "Common/Components/Sortable/SortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "Core.Themis.Libraries.Razor.snpnbh2cmw.styles.css", "AssetFile": "Core.Themis.Libraries.Razor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "886"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNCuKZyfclYsS+a1pD2qGQrvVP2+TPU1yzLFuMSKHaU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:55 GMT"}, {"Name": "Link", "Value": "<_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "snpnbh2cmw"}, {"Name": "integrity", "Value": "sha256-qNCuKZyfclYsS+a1pD2qGQrvVP2+TPU1yzLFuMSKHaU="}, {"Name": "label", "Value": "Core.Themis.Libraries.Razor.styles.css"}]}, {"Route": "Core.Themis.Libraries.Razor.styles.css", "AssetFile": "Core.Themis.Libraries.Razor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "886"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNCuKZyfclYsS+a1pD2qGQrvVP2+TPU1yzLFuMSKHaU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:55 GMT"}, {"Name": "Link", "Value": "<_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNCuKZyfclYsS+a1pD2qGQrvVP2+TPU1yzLFuMSKHaU="}]}, {"Route": "_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css", "AssetFile": "_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10857"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1p2vug7PF4nK8g3cCr3/Gge2gk18Zm5z3uBJvJgYmTc=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 15:56:12 GMT"}], "EndpointProperties": []}, {"Route": "_content/BlazorDateRangePicker/clickAndPositionHandler.js", "AssetFile": "_content/BlazorDateRangePicker/clickAndPositionHandler.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5344"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y13FGi0LwdilZTDiSJhwO9kI/dHRax0/H52S+Yq1Ae0=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 17:14:55 GMT"}], "EndpointProperties": []}, {"Route": "css/common.fslu2o6p56.less", "AssetFile": "css/common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fslu2o6p56"}, {"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}, {"Name": "label", "Value": "css/common.less"}]}, {"Route": "css/common.less", "AssetFile": "css/common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}]}, {"Route": "css/widget-modules/button.css", "AssetFile": "css/widget-modules/button.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "712"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tND8pTCCoF4iIleIh1EoS3AwrWBS5dTr2wmcM9dPYqA=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tND8pTCCoF4iIleIh1EoS3AwrWBS5dTr2wmcM9dPYqA="}]}, {"Route": "css/widget-modules/button.kkli7vhhk7.css", "AssetFile": "css/widget-modules/button.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "712"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tND8pTCCoF4iIleIh1EoS3AwrWBS5dTr2wmcM9dPYqA=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kkli7vhhk7"}, {"Name": "integrity", "Value": "sha256-tND8pTCCoF4iIleIh1EoS3AwrWBS5dTr2wmcM9dPYqA="}, {"Name": "label", "Value": "css/widget-modules/button.css"}]}, {"Route": "css/widget-modules/loader.0p7mpp8irk.css", "AssetFile": "css/widget-modules/loader.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1514"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jHJh+dHcP6SVkkPrWPN/TsV8Xjb9zzylU4Cnw1Kx/2g=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0p7mpp8irk"}, {"Name": "integrity", "Value": "sha256-jHJh+dHcP6SVkkPrWPN/TsV8Xjb9zzylU4Cnw1Kx/2g="}, {"Name": "label", "Value": "css/widget-modules/loader.css"}]}, {"Route": "css/widget-modules/loader.css", "AssetFile": "css/widget-modules/loader.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1514"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jHJh+dHcP6SVkkPrWPN/TsV8Xjb9zzylU4Cnw1Kx/2g=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jHJh+dHcP6SVkkPrWPN/TsV8Xjb9zzylU4Cnw1Kx/2g="}]}, {"Route": "css/widget-modules/modal.css", "AssetFile": "css/widget-modules/modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3905"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7laGy4P5g8IZUbHhmyWoRnFI85qJbdyTUKpW6a8Lik4=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7laGy4P5g8IZUbHhmyWoRnFI85qJbdyTUKpW6a8Lik4="}]}, {"Route": "css/widget-modules/modal.e0ah2qis0d.css", "AssetFile": "css/widget-modules/modal.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3905"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7laGy4P5g8IZUbHhmyWoRnFI85qJbdyTUKpW6a8Lik4=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e0ah2qis0d"}, {"Name": "integrity", "Value": "sha256-7laGy4P5g8IZUbHhmyWoRnFI85qJbdyTUKpW6a8Lik4="}, {"Name": "label", "Value": "css/widget-modules/modal.css"}]}, {"Route": "css/widget.css", "AssetFile": "css/widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26322"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"msC/tXY8lR7f4Ouc8hclV/Y8EhM/WEnJLVox66pd0IE=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-msC/tXY8lR7f4Ouc8hclV/Y8EhM/WEnJLVox66pd0IE="}]}, {"Route": "css/widget.ems2yguf34.css", "AssetFile": "css/widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26322"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"msC/tXY8lR7f4Ouc8hclV/Y8EhM/WEnJLVox66pd0IE=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ems2yguf34"}, {"Name": "integrity", "Value": "sha256-msC/tXY8lR7f4Ouc8hclV/Y8EhM/WEnJLVox66pd0IE="}, {"Name": "label", "Value": "css/widget.css"}]}, {"Route": "js/common_razor_library.js", "AssetFile": "js/common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4631"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pr8Iwr9C1e6/OYArBA4IghWrBUDHBG385UIHcrcWTfM=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pr8Iwr9C1e6/OYArBA4IghWrBUDHBG385UIHcrcWTfM="}]}, {"Route": "js/common_razor_library.y6ih6pw0x9.js", "AssetFile": "js/common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4631"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pr8Iwr9C1e6/OYArBA4IghWrBUDHBG385UIHcrcWTfM=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y6ih6pw0x9"}, {"Name": "integrity", "Value": "sha256-pr8Iwr9C1e6/OYArBA4IghWrBUDHBG385UIHcrcWTfM="}, {"Name": "label", "Value": "js/common_razor_library.js"}]}, {"Route": "js/phoneInput.5yj6xghg17.js", "AssetFile": "js/phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5yj6xghg17"}, {"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}, {"Name": "label", "Value": "js/phoneInput.js"}]}, {"Route": "js/phoneInput.js", "AssetFile": "js/phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}]}, {"Route": "js/widget.gi8lcgmc3a.js", "AssetFile": "js/widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17834"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLVhAxc0fn+KHQ+tAHdXcEvnopeAbJIA5+0XHBAVyK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gi8lcgmc3a"}, {"Name": "integrity", "Value": "sha256-qLVhAxc0fn+KHQ+tAHdXcEvnopeAbJIA5+0XHBAVyK8="}, {"Name": "label", "Value": "js/widget.js"}]}, {"Route": "js/widget.js", "AssetFile": "js/widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17834"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLVhAxc0fn+KHQ+tAHdXcEvnopeAbJIA5+0XHBAVyK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 14:32:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qLVhAxc0fn+KHQ+tAHdXcEvnopeAbJIA5+0XHBAVyK8="}]}]}