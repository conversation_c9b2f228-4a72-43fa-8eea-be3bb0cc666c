﻿@using Core.Themis.Libraries.Razor.Areas.Catalog.ViewModels;
@using Core.Themis.Libraries.Razor.Areas.Catalog;

@model CatalogAppSettings

@{
    ViewBag.Title = "Catalog";
    string UrlToPath = $"{@Context.Request.Scheme}://{@Context.Request.Host}{@Context.Request.PathBase}";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
}


@section styles {
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/catalog/style.less">
}

<component type="typeof(CatalogApp)" render-mode="Server" param-AppSettings="@Model" />


@*************** SCRIPTS *************@
@section scripts {
    <script src="@(UrlToPath)js/catalog/catalog.js"></script>
    <script>
        var htmlSelector = "@Model.HtmlSelector";
        var structureId = "@Model.StructureId";
        var langCode = "@Model.LangCode";
    </script>
}