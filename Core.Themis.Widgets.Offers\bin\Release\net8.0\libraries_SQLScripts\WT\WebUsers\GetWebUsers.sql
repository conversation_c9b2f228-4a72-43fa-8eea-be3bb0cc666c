﻿

DECLARE @PageNumber AS INT
DECLARE @RowsOfPage AS INT
SET @PageNumber= @ppageindex  +1
SET @RowsOfPage=@pcount


DECLARE @statesPossible table (et varchar(3))
insert into @statesPossible values ('ALL')
insert into @statesPossible values ('P')
insert into @statesPossible values ('V')
insert into @statesPossible values ('C')
insert into @statesPossible values ('I')
insert into @statesPossible values ('PP')
insert into @statesPossible values ('E') -- erreur
insert into @statesPossible values ('VP')
insert into @statesPossible values ('R')
insert into @statesPossible values ('NS')

SELECT distinct et
INTO #etatCherche 
FROM @statesPossible where et in ({pstates})

DECLARE @ChercheNoEtat int 
SELECT @ChercheNoEtat = COUNT(*) from #etatCherche where et='NS'

DECLARE @ChercheErreur int 
SELECT @ChercheErreur = COUNT(*) from #etatCherche where et='E'

DECLARE @ChercheAllEtat int 
SELECT @ChercheAllEtat = COUNT(*) from #etatCherche where et='ALL'
IF (@ChercheAllEtat>0)
BEGIN
    insert into #etatCherche
    select et from @statesPossible
END



DECLARE @etatChercheExist int 
SELECT @etatChercheExist = COUNT(*) from #etatCherche

IF (@ChercheNoEtat > 0)
BEGIN

    SELECT
		    user_id,
          [sessionweb_id]      
          ,users.identite_id      
	      ,[start_date]
          ,[adresseIP]
          ,[urlreferer]
          ,users.structure_id
          ,[browser]
          ,[application_path]
          ,[profil_acheteur_id]
          ,[queuingTicket],

     (SELECT max(log_date) FROM logs l where l.user_id=users.user_id) AS log_last_date,
     (SELECT count(*) FROM logs l where l.user_id=users.user_id) AS nLogs,
     (SELECT count(*) FROM logs l  
	    INNER JOIN messages_types mt on mt.message_type_id = l.message_type_id INNER JOIN logs_types lt on mt.type_log_id = lt.type_log_id   
	    WHERE l.user_id=users.user_id and lt.type_log_libelle='Erreur') AS nLogsErreurs, 
     (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('C','R','T')) AS basketsCreation , 
     (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat='I') AS basketsIna, 
     (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('P','E', 'X')) AS basketsPayes, 
     (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('V','VP','Q')) AS basketsValides 

    FROM users 
    LEFT JOIN panier p on p.web_user_id = users.user_id
        WHERE 
            users.start_date >= @pdate1
            and users.start_date < @pdate2

    [where_identite]
    [where_ip]
    [where_order]
    [where_basket]
    [where_email]
    [where_transaction]

    ORDER by user_id DESC

    OFFSET (@PageNumber-1)*@RowsOfPage ROWS
    FETCH NEXT @RowsOfPage ROWS ONLY

END
ELSE
BEGIN

	IF (@ChercheErreur = 0)
    BEGIN
        SELECT 
		        user_id,
              [sessionweb_id]      
              ,users.identite_id      
	          ,[start_date]
              ,[adresseIP]
              ,[urlreferer]
              ,users.structure_id
              ,[browser]
              ,[application_path]
              ,[profil_acheteur_id]
              ,[queuingTicket],

         (SELECT max(log_date) FROM logs l where l.user_id=users.user_id) AS log_last_date,
         (SELECT count(*) FROM logs l where l.user_id=users.user_id) AS nLogs,
         (SELECT count(*) FROM logs l  
	        INNER JOIN messages_types mt on mt.message_type_id = l.message_type_id INNER JOIN logs_types lt on mt.type_log_id = lt.type_log_id   
	        WHERE l.user_id=users.user_id and lt.type_log_libelle='Erreur') AS nLogsErreurs, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('C','R','T')) AS basketsCreation , 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat='I') AS basketsIna, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('P','E', 'X')) AS basketsPayes, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('V','VP','Q')) AS basketsValides 

             FROM users 
	    INNER JOIN panier p ON p.web_user_id = users.user_id
	    INNER JOIN #etatCherche etc ON etc.et = p.etat

             WHERE 
            users.start_date >= @pdate1
            and users.start_date < @pdate2

            [where_identite]
            [where_ip]
            [where_order]
            [where_basket]
            [where_email]
            [where_transaction]

    	    GROUP BY 
	            user_id,
		      [sessionweb_id]      
		      ,users.identite_id      
		      ,[start_date]
		      ,[adresseIP]
		      ,[urlreferer]
		      ,users.structure_id
		      ,[browser]
		      ,[application_path]
		      ,[profil_acheteur_id]
		      ,[queuingTicket]


        ORDER by user_id DESC

        OFFSET (@PageNumber-1)*@RowsOfPage ROWS
        FETCH NEXT @RowsOfPage ROWS ONLY
    END
    ELSE
    BEGIN -- chercher les paniers en erreurs
           SELECT 
		        user_id,
              [sessionweb_id]      
              ,users.identite_id      
	          ,[start_date]
              ,[adresseIP]
              ,[urlreferer]
              ,users.structure_id
              ,[browser]
              ,[application_path]
              ,[profil_acheteur_id]
              ,[queuingTicket],

         (SELECT max(log_date) FROM logs l where l.user_id=users.user_id) AS log_last_date,
         (SELECT count(*) FROM logs l where l.user_id=users.user_id) AS nLogs,
         (SELECT count(*) FROM logs l  
	        INNER JOIN messages_types mt on mt.message_type_id = l.message_type_id INNER JOIN logs_types lt on mt.type_log_id = lt.type_log_id   
	        WHERE l.user_id=users.user_id and lt.type_log_libelle='Erreur') AS nLogsErreurs, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('C','R','T')) AS basketsCreation , 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat='I') AS basketsIna, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('P','E', 'X')) AS basketsPayes, 
         (SELECT count(*) FROM panier p WHERE p.web_user_id=users.user_id AND etat in ('V','VP','Q')) AS basketsValides 

             FROM users 
	    INNER JOIN panier p ON p.web_user_id = users.user_id	    
        INNER JOIN logs_etapes_creationCmds l on l.panier_id = p.panier_id AND l.etat=99
        WHERE 
            users.start_date >= @pdate1
            and users.start_date < @pdate2

            [where_identite]
            [where_ip]
            [where_order]
            [where_basket]
            [where_email]
            [where_transaction]

    	    GROUP BY 
	            user_id,
		      [sessionweb_id]      
		      ,users.identite_id      
		      ,[start_date]
		      ,[adresseIP]
		      ,[urlreferer]
		      ,users.structure_id
		      ,[browser]
		      ,[application_path]
		      ,[profil_acheteur_id]
		      ,[queuingTicket]


        ORDER by user_id DESC

        OFFSET (@PageNumber-1)*@RowsOfPage ROWS
        FETCH NEXT @RowsOfPage ROWS ONLY
    END

END

DROP TABLE #etatCherche

