﻿--declare @identityId int
--set @identityId = 18
/* 0281 *********************************  */
/* filtrer sur formule 42 + exclusre le tarif 2810010 */

Declare @Manif_id int
Declare @Commande_id int

Declare @SQL varchar(max)
Declare @Tmptable as table (Commande_id int , Date_resa date,Nb_resa int, Etat varchar(50), 
Manif_id int, seance_id int ,dossier_id int
, formule_id int, abo_id int, Mnt_CalculeApartirDesCols int)



DECLARE cursor_reservations CURSOR
FOR

select distinct  cl.manifestation_id,commande_id  
from Commande_Ligne cl 
inner join seance s on s.seance_Id = cl.seance_id
where identite_id =@identityId and cl.manifestation_id > 0 
and s.seance_date_deb>DATEADD(YEAR,0,GETDATE())

OPEN cursor_reservations

FETCH NEXT FROM cursor_reservations INTO @Manif_id,@Commande_id

WHILE @@FETCH_STATUS = 0
BEGIN

/* and dossier_nbplace =1  à la fin car pour l'instant on ne gere que les abo à coeff =1 */


set @SQL ='select clc.commande_id as commande_id
		,convert( date,dsvg.date_operation) as Date_resa
		,1 as Nb_résas
		,clc.Etat 
		--,dsvg.dossier_montant * 100  as Mnt_résa
		--,case when clc.Etat = ''R'' then dsvg.dossier_montant *100 else 0 end as Mnt_a_payer
		,clc.manifestation_id, clc.seance_id, clc.dossier_id,cmdl.formule_id, cmdl.abo_id
		,esvg.montant1 * 100 + esvg.montant2 * 100 +
		
                            case  when modecol4=''REMISE''  then - esvg.montant4* 100 
                        				when modecol4=''TAXE'' or modecol4=''COMMISSION'' then + esvg.montant4* 100 
                        				else 0 END 
                        				+
                         case  when modecol5=''REMISE''  then - esvg.montant5 * 100 
                        				when modecol5=''TAXE'' or modecol5=''COMMISSION'' then + esvg.montant5* 100 
                        				else 0 END +
                         case  when modecol6=''REMISE''   then - esvg.montant6 * 100 
                        				when modecol6=''TAXE'' or modecol6=''COMMISSION'' then + esvg.montant6* 100 
                        				else 0 END +
                        case  when modecol7=''REMISE''  then - esvg.montant7 * 100 
                        				when modecol7=''TAXE'' or modecol7=''COMMISSION'' then + esvg.montant7* 100 
                        				else 0 END +
                         case  when modecol8=''REMISE'' then - esvg.montant8 * 100 
                        				when modecol8=''TAXE'' or modecol8=''COMMISSION''  then + esvg.montant8* 100 
                        				else 0 END +
                         case  when modecol9=''REMISE''  then - esvg.montant9 * 100 
                        				when modecol9=''TAXE''  or modecol9=''COMMISSION'' then + esvg.montant9* 100 
                        				else 0 END +
                         case  when modecol10=''REMISE''  then - esvg.montant10 * 100 
                        				when modecol10=''TAXE'' or modecol10=''COMMISSION'' then + esvg.montant10* 100 
                        				else 0 END as Mnt_CalculeApartirDesCols
		FROM dossiersvg_'+ replace(STR(@Manif_id),' ','') +' dsvg inner join structure struc on 1=1 
					iNNER join dossier_'+ replace(STR(@Manif_id),' ','') +' d on d.dossier_id = dsvg.dossier_id and d.seance_id = dsvg.seance_id 
					inner join entreesvg_'+ replace(STR(@Manif_id),' ','') +' esvg on esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v									
					inner join commande_ligne cmdl ON cmdl.commande_id=dsvg.commande_id AND cmdl.dossier_id=dsvg.dossier_id  and cmdl.seance_id=dsvg.seance_Id	
					inner join Commande_Ligne_comp clc on clc.commande_ligne_id = cmdl.commande_ligne_id		
					WHERE dsvg.identite_id = '+ replace(STR(@identityId),' ','') +' and 
					type_ligne=''DOS'' 
					--and esvg.type_tarif_id <> 2810010 
					 AND (dsvg.dossier_etat = ''R'' or dsvg.type_operation like  ''RESA%'') and dsvg.commande_id = '+ replace(STR(@Commande_id),' ','') +'
					 AND d.dossier_etat =''R'' '

insert into @Tmptable exec (@SQL)

print @SQL

FETCH NEXT FROM cursor_reservations INTO @Manif_id,@Commande_id

END

CLOSE cursor_reservations
DEALLOCATE cursor_reservations


  /***********************************************/

SELECT sum(tt.Nb_resa) as 'nb_seats', 0 as nb_products , tt.commande_id,tt.date_resa,sum(tt.Nb_resa) as nb_article,sum(tt.Mnt_CalculeApartirDesCols) as amount_reserved ,sum(tt.Mnt_CalculeApartirDesCols) as amount_to_pay,'DOS' as 'type',	
	case when SUM(tt.Mnt_CalculeApartirDesCols - tt.Mnt_CalculeApartirDesCols) = 0 then 0 else  1 end as isPartiallyPaid,
	(select date_limite 
	from dossier_produit_reservation prodresa 
	inner join dossier_produit dp on dp.dos_prod_id = prodresa.dos_prod_id and dp.dos_prod_etat='P' and dp.commande_id = tt.Commande_id) 
	as date_limite, formule_id, abo_id  
 INTO #resultTable 
 from @Tmptable tt
 
 --where tt.Mnt_a_payer > 0
group by tt.commande_id,tt.date_resa ,formule_id, abo_id

 UNION
 
 SELECT  0 as nbSeats, sum(dpsvg.nb_produit) as nb_products, dpsvg.commande_id,convert(date,dpsvg.date_operation) as date_resa,sum(dpsvg.nb_produit) as nb_article,
 convert(int ,SUM(dpsvg.dos_prod_montant)) * 100 as amount_reserved ,convert(int,SUM(dpsvg.dos_prod_montant - dp.dos_prod_montant)) * 100 as amount_to_pay ,'PRO' as 'type',
 case when SUM( (dpsvg.dos_prod_montant) - (dpsvg.dos_prod_montant - dp.dos_prod_montant) ) = 0 and sum(dpsvg.dos_prod_montant)>0 then 0 else  1 end as isPartiallyPaid,
 (select date_limite from dossier_produit_reservation prodresa 
	inner join dossier_produit dp on dp.dos_prod_id = prodresa.dos_prod_id and dp.dos_prod_etat='P' and dp.commande_id = dpsvg.Commande_id) as date_limite 
  	,null, null
  from dossier_produitsvg dpsvg 
 inner join dossier_produit dp on dp.dos_prod_id = dpsvg.dos_prod_id and dp.dos_prod_v = dpsvg.dos_prod_v
 where dpsvg.identite_id = @identityId
and dp.dos_prod_etat = 'R' 
 --and (dpsvg.dos_prod_montant - dp.dos_prod_montant) > 0
  group by dpsvg.commande_id ,convert(date,dpsvg.date_operation)  

  order by commande_id desc

  /***********************************************/


SELECT  SUM(nb_seats) as nb_seats, SUM(nb_products) as nb_products, commande_id, Date_resa, SUM(nb_article) as nb_article, SUM(amount_reserved) as amount_reserved, SUM(amount_to_pay) as amount_to_pay, 'PRO' as type, 
   sum(isPartiallyPaid) as isPartiallyPaid, date_limite, SUM(formule_id) as formule_id, SUM(abo_id) as abo_id ,

	 case when date_limite>GETDATE() then 'ok' else 
		case when date_limite is null /** date limite is null ET il y a un produit resa */
		and (select sum(prod.produit_id) from dossier_produit dp 
   inner join produit prod on prod.produit_id = dp.produit_id 
    where dp.commande_id = #resultTable.commande_id and prod.groupe_id = 12) is not null
		then 'ok' else 
		'no'  
		end 	 
	 end as date_limite_ok 	
	
	 from #resultTable
	 GROUP BY Commande_id, Date_resa, date_limite
	 order by Commande_id desc

   DROP TABLE #resultTable


