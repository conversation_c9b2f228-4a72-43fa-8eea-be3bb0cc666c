﻿$(window).resize(function () {
	if ($(window).outerWidth(true).toString() != $('body').attr('oldwidth')) {
		$('body').attr('oldwidth', $(window).outerWidth(true).toString())
		initResizeEventsOverlay(showImage)
	}
});

function initGridList(showImage) {
	$('[data-viewmode]').off('click').on('click', function () {
		var bigwrapper = $(this).closest("[data-eventareaconfigcount]")
		var mode = $(this).attr("data-viewmode")
		$(bigwrapper).find('[data-viewmode]').removeClass('active')
		$(this).addClass('active')
		$.each($(bigwrapper).find('[data-viewmode]'), function (i, k) {
			$(bigwrapper).find('.catalog_offers').removeClass($(k).attr("data-viewmode"))
		})
		$(bigwrapper).find('.catalog_offers').addClass(mode)
		initResizeEventsOverlay(showImage)
	})

	$('[data-viewmode].active').trigger('click')
}

function initResizeEventsOverlay(showImage) {
	$('.offer_one_front').css({
		"height": ""
	})
	const currentBreakpoint = bootstrapDetectBreakpoint()
	if (currentBreakpoint.index >= 2) {
		
		$.each($('.catalog_offers.grid'), function (i, k) {
			//
			maxheight = 0;
			
			/*if (showImage == false) {
				//si l'image des évènements est masquée
				$(k).find('.offer_one .offer_one_back').css({ "opacity": 1, "position": "relative", "height": "100%" })
				$(k).find('.offer_one .offer_one_front').css({ "display": "none" })
				$.each($(k).find('.offer_one .offer_one_back'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.offer_one .offer_one_back').css({
					"height": maxheight
				})
			} else {
				//si l'image des évènements est affichée
				$.each($(k).find('.offer_one .offer_one_front'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.offer_one .offer_one_front').css({
					"height": maxheight
				})
			}*/

			if (showImage == true) {
				//si l'image des évènements est affichée
				$.each($(k).find('.offer_one .offer_one_front'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.offer_one .offer_one_front').css({
					"height": maxheight
				})
			}

			$(k).attr('initial-cards-height', maxheight)
			$(k).attr('initial-height', $(k).outerHeight())
			
		})

		//si l'image des évènements est affichée
		if (showImage == true) {
			$('.catalog_offers.grid .offer_one').off("mouseenter").on("mouseenter", function () {
				var front = $(this).find('.offer_one_front')
				var back = $(this).find('.offer_one_back')

				var backMaxHeight = $(back).outerHeight() + parseInt($(back).css('border-top-width')) + parseInt($(back).css('border-bottom-width'))

				var dif = 0
				if (backMaxHeight > $(front).outerHeight()) {
					dif = backMaxHeight - parseFloat($(front).css("height"))
					if (($(this).position().top + backMaxHeight > ($(this).closest('.catalog_offers').position().top + parseFloat($(this).closest('.catalog_offers').attr('initial-height'))))) {
						var catalog_offersHeight = $(this).closest('.catalog_offers').outerHeight() + dif
						$(front).css({
							"height": backMaxHeight
						})
						sendIframeSize(dif)
					}
				}

				if (backMaxHeight < $(front).outerHeight()) {
					$(back).css({
						"height": $(front).outerHeight()
					})
				}

			}).off("mouseleave").on("mouseleave", function () {
				var front = $(this).find('.offer_one_front')
				var back = $(this).find('.offer_one_back')
				$(back).css({
					"height": ""
				})
				$(front).css({
					"height": $(this).closest('.catalog_offers').attr('initial-cards-height')
				})
				sendIframeSize()
			});
		} 
		sendIframeSize()
	} else {
		$('.offer_one').off("mouseenter").off("mouseleave")
		sendIframeSize()
	}
}

function bindClickDataHref() {
	$("[data-href]:not([data-href='']").off('click').on('click', function () {
		var msg = {}
		
			switch ($(this).attr('data-action')) {
				case "openwaitinglist":
					msg = {
						"action": $(this).attr('data-action'),
						"url": $(this).attr('data-href')
					}
					break;
				default:
					msg = {
						"action": "urltogo",
						"url": $(this).attr('data-href')
					}
					break;
			}
		

		window.parent.postMessage(msg, '*')
	})

	$("[data-urltogo]:not([data-urltogo='']").off('click').on('click', function () {
		var msg = {}
		if ($(this).attr('data-urltogo') != undefined) {
			msg = {
				"action": "urltogo",
				"urltogo": $(this).attr('data-urltogo')
			}
		}
		window.parent.postMessage(msg, '*')
	})
}