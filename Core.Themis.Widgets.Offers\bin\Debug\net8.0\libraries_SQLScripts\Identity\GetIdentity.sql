﻿/*


declare @pIdentiteId int = 29
declare @pStructureId int = 987


declare @padherentId int = 0

*/ 



CREATE TABLE #tblIdentitiesId  (identiteId INT)


DECLARE @myidentite_id int

	declare @reversidentite_id int
	set @reversidentite_id = dbo.F_adhesion_num_reverse(@padherentId)
	if (@reversidentite_id is not null)
	BEGIN 
		set @myidentite_id = @reversidentite_id

	
	    INSERT INTO #tblIdentitiesId
	    SELECT identite_id FROM identite WHERE identite_id =@myidentite_id;
	END

    /* liste identites ou liste emails */

	INSERT INTO #tblIdentitiesId
	SELECT identite_id FROM identite WHERE identite_id IN ({pIdentiteId}) 
		UNION
	SELECT identite_id FROM identite WHERE 
		postal_tel[COLEMAIL] IN ({pEmails}) and postal_tel[COLEMAIL]<>''



SELECT DISTINCT
(SELECT TOP 1 ic.libelle from identite_infos_comp iic 
	inner join info_comp ic on ic.info_comp_id = iic.info_comp_id 
	WHERE libelle IN ('PERSONNE_PHYSIQUE','PERSONNE_MORALE') AND iic.identite_id = i.identite_id
	ORDER BY iic.id desc
) AS contact_type
      , aa.Adherent_ID
      ,i.identite_id
      ,i.identite_nom
      ,i.identite_complement
      ,i.identite_titre_id
      ,i.appellation_id
      ,i.identite_date_naissance
      ,i.postal_rue1
      ,i.postal_rue2
      ,i.postal_rue3
      ,i.postal_rue4
      ,i.postal_cp
      ,i.postal_ville
      ,i.postal_region
      ,i.postal_pays
      ,i.postal_tel1
      ,convert(int,i.postal_tel1_libelle_id) as postal_tel1_libelle_id
      ,i.postal_tel2
      ,convert(int,i.postal_tel2_libelle_id) as postal_tel2_libelle_id
      ,i.postal_tel3
      ,convert(int,i.postal_tel3_libelle_id) as postal_tel3_libelle_id
      ,i.postal_tel4
      ,convert(int,i.postal_tel4_libelle_id) as postal_tel4_libelle_id
      ,i.postal_tel5
      ,convert(int,i.postal_tel5_libelle_id) as postal_tel5_libelle_id
      ,i.facture_rue1
      ,i.facture_rue2
      ,i.facture_rue3
      ,i.facture_rue4
      ,i.facture_cp
      ,i.facture_ville
      ,i.facture_region
      ,i.facture_pays
      ,i.facture_tel1
      ,i.facture_tel1_libelle_id
      ,i.facture_tel2
      ,i.facture_tel2_libelle_id
      ,i.facture_tel3
      ,i.factue_tel3_libelle_id
      ,i.facture_tel4
      ,i.facture_tel4_libelle_id
      ,i.marqueur_mailing_id
      ,i.identite_validite_debut
      ,i.identite_validite_fin
      ,i.identite_v
      ,i.operateur_id
      ,i.ref_compta
      ,i.ref_perso
      ,i.etiquette1
      ,i.etiquette2
      ,i.etiquette3
      ,i.etiquette4
      ,i.etiquette5
      ,i.identite_libre1
      ,i.identite_libre2
      ,i.identite_libre3
      ,i.identite_libre4
      ,i.identite_libre5
      ,i.postal_tel6
      ,convert(int,i.postal_tel6_libelle_id) as postal_tel6_libelle_id
      ,i.postal_tel7
      ,convert(int,i.postal_tel7_libelle_id) as postal_tel7_libelle_id
      ,i.identite_prenom
      ,i.filiere_id
      ,i.identite_remise
      ,i.identite_remisedg
      ,i.montant_credit
      ,i.montant_debit
      ,i.FicheSupprimer
      ,i.statut_financier
      ,i.appelinterloc_id
      ,i.identite_groupe_id
      ,i.identite_nomprenom
      ,i.IDENTITE_DATE_CREATION
      ,i.IDENTITE_DATE_MODIFICATION
      ,i.operateurmodif_id
      ,i.facture_tel5
      ,i.facture_tel5_libelle_id
      ,i.facture_tel6
      ,i.facture_tel6_libelle_id
      ,i.facture_tel7
      ,i.facture_tel7_libelle_id
      ,i.identite_password, ic.commentaire, ic.langue_id, ic.nationality
FROM #tblIdentitiesId tmpIdent 
INNER JOIN identite i ON i.identite_id = tmpIdent.identiteId 
LEFT OUTER JOIN Adhesion_Adherent aa ON aa.Identite_id = i.identite_id
LEFT OUTER JOIN identite_complement ic ON i.identite_id= ic.identite_id
ORDER BY i.identite_id;


DROP TABLE #tblIdentitiesId

/******* liste libelle tel */
SELECT * FROM Libelle_tel

/*
SELECT *,  from #tblIdentitiesId tmpIdent 
inner join identite i on i.identite_id = tmpIdent.identite_id
left outer join identite_infos_comp iic on iic.identite_id = i.identite_id
order by identite_id;
*/
