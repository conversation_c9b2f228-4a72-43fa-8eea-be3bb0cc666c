﻿
    select tfgt.id as global_field_code_id, tfgt.fieldCode as global_field_code, tfc.fieldSpecificCode as field_code, tfc.id as field_code_id, tfc.description as field_code_description, ta.id as area_id, name as area_name
  from translate_fieldsGlobalTranslation tfgt
  inner join translate_fieldsCodesList tfc on tfc.global_field_id = tfgt.id and tfc.fieldSpecificCode =  tfgt.fieldCode
  inner join translate_areas ta on ta.id = tfc.area_id