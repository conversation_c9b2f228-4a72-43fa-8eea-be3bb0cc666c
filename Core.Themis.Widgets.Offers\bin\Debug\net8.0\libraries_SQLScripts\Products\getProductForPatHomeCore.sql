﻿
/*
declare @plangcode varchar(2) = 'fr'
declare @pproductId int = 9940041
declare @porderId int = 0
declare @pdosprodId int = 123
declare @precetteId int = 5

declare @pbat int = 0
*/

 declare @typeDatecourte int = 103 -- angl par defaut
 if lower(@plangcode) = 'de' begin set @typeDatecourte = 104 end -- de
 if lower(@plangcode) = 'it' begin set @typeDatecourte = 105 end -- it


-- Gestion de la langue
DECLARE @langue_Id INT = COALESCE(
    (SELECT langue_id FROM langue WHERE UPPER(langue_code) = UPPER(@plangcode)),
    0
);

-- Structure conditionnelle unifiée
IF OBJECT_ID('tempdb..#Results') IS NOT NULL DROP TABLE #Results;

WITH BaseQuery AS (
    SELECT
        dp.dos_prod_id AS dossier_id,
        COALESCE(tp.produit_nom, p.produit_nom) AS produit_nom,
        COALESCE(tp.produit_descrip, p.produit_descrip) AS produit_info_1,
        COALESCE(f.identifiant, 'FBI123456789123') AS FEEDBOOK_IDENTIFIANT,
        FORMAT(COALESCE(f.validity_end, DATEADD(YEAR, 1, GETDATE())), 'dd/MM/yy') AS FEEDBOOK_VALIDITY_END,

	CASE 
    WHEN @pbat = 0 THEN
        CONCAT(
            '50', 
            RIGHT('000000' + CAST(dp.commande_id AS VARCHAR(6)), 6),
            RIGHT('000000' + CAST(dp.dos_prod_id AS VARCHAR(6)), 6),
            RIGHT(
                '000' + CAST((
                    SELECT COUNT(*) 
                    FROM commande_ligne cl2
                    INNER JOIN recette_produit r2 
                        ON r2.produit_stock_id = cl2.dossier_id 
                        AND r2.seance_id = cl2.seance_id
                    WHERE cl2.Commande_id = dp.commande_id 
                        AND type_ligne = 'PRO' 
                        AND cl2.dossier_id = dp.dos_prod_id
                ) AS VARCHAR(3)), 3
            )
        )
		ELSE 
        '50123456123456999'
		END AS CODEBARRE_UNIQUE,
        -- Section montants
        CONVERT(VARCHAR(20), CAST(dos_prod_montant AS DECIMAL(18,2))) AS produit_montant_total_eu,
        CONVERT(VARCHAR(20), CAST((dos_prod_montant1 + dos_prod_montant2) AS DECIMAL(18,2))) AS produit_montant_total_eu2,
		CONVERT(VARCHAR(20),CAST(p.vts_grille1 as decimal(18,2))) as produit_montant_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille2 as decimal(18,2))) as produit_montant_2_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille3 as decimal(18,2))) as produit_montant_3_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille4 as decimal(18,2))) as produit_montant_4_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille5 as decimal(18,2))) as produit_montant_5_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille6 as decimal(18,2))) as produit_montant_6_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille7 as decimal(18,2))) as produit_montant_7_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille8 as decimal(18,2))) as produit_montant_8_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille9 as decimal(18,2))) as produit_montant_9_eu,
		CONVERT(VARCHAR(20),CAST(p.vts_grille10 as decimal(18,2))) as produit_montant_10_eu,
        CONVERT(varchar(20),CAST((dos_prod_montant) as decimal(18,2))) as produit_dossier_montant_total_eu, 
            p.unbilletparproduit as unbilletparproduit, 

        CONVERT(varchar(50), dpr.Date_Deb_Validite, @typeDatecourte)  as DATE_DEB_VALIDITE_KDO,
        CONVERT(varchar(50), dpr.Date_Fin_Validite, @typeDatecourte)  as DATE_FIN_VALIDITE_KDO,

        dpr.Reference as REF_KDO          
        ,iic.valeur3 as ADHESION_VALIDITE,

        r.recette_id
    FROM produit p
    LEFT JOIN traduction_produit tp 
        ON tp.produit_id = p.produit_id 
        AND tp.langue_id = @langue_Id
    LEFT JOIN dossier_produit dp 
        ON p.produit_id = dp.produit_id
        AND (@pbat = 1 OR dp.commande_id = @porderId)
    LEFT JOIN feedbooks f 
        ON f.dossier_produit_id = dp.dos_prod_id
    LEFT JOIN recette_produit r 
        ON dp.dos_prod_id = r.produit_stock_id
    LEFT JOIN Dossier_Produit_Ref dpr 
        ON dpr.Dos_prod_id = dp.dos_prod_id
    LEFT OUTER JOIN identite_infos_comp iic on  iic.info_comp_id = p.infocomp_id and iic.identite_id = dp.identite_id and iic.supprimer = 'N'

    WHERE p.produit_id = @pproductId
)

SELECT *
INTO #Results
FROM BaseQuery


IF @pbat = 0
BEGIN
    
    DELETE FROM #Results WHERE dossier_id is null OR recette_id is null OR dossier_id <> @pdosprodId OR (@precetteId <> 0 and recette_id <> @precetteId);    
   
END
ELSE
    UPDATE #Results SET produit_montant_total_eu = produit_montant_eu, produit_montant_total_eu2 = produit_montant_eu + produit_montant_2_eu

-- Résultat final

 SELECT top 1 * FROM #Results;

DROP TABLE #Results;



 