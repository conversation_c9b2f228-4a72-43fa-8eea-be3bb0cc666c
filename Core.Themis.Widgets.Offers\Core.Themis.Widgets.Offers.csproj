﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <CopyRefAssembliesToPublishDirectory>false</CopyRefAssembliesToPublishDirectory>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	
  <ItemGroup>
    <Content Remove="nlog.config" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="config.ncconf" />
  </ItemGroup>

  <ItemGroup>
    <None Include="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="wwwroot\css\catalog\style.less" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazor.Bootstrap" Version="3.2.0" />
    <PackageReference Include="CSharpMinifier" Version="1.2.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.6" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
    <PackageReference Include="NUglify" Version="1.21.17" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    
    <ProjectReference Include="..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\websiteexterneDemo\" />
  </ItemGroup>

</Project>
