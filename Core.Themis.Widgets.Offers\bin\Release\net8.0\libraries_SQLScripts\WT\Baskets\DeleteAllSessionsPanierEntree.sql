/* -------- DeleteAllSessionsPanierEntree.sql --------------


declare @pBasketId int 
set @pBasketId =322676

declare @pSessionId int 
set @pSessionId =30
*/


DELETE FROM panier_entree WHERE panier_id = @pBasketId and seance_id = @pSessionId and entree_id in ({pSeatsId}) and panier_id in (select panier_id from panier where etat in ('C','I') and panier_id = @pBasketId)

SELECT count(*) as restant FROM panier_entree WHERE panier_id = @pBasketId and seance_id = @pSessionId and entree_id in ({pSeatsId})