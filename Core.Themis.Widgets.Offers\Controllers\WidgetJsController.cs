﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NUglify;
using System;
using System.IO;

namespace Core.Themis.Widgets.Offers.Controllers
{
    [Route("widget-js")]
    public class WidgetJsController : Controller
    {

        private readonly ILogger<WidgetJsController> _logger;

        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IWebHostEnvironment _hostingEnvironment;
        public WidgetJsController(ILogger<WidgetJsController> logger, IHttpContextAccessor httpContextAccessor, IWebHostEnvironment hostingEnvironment)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _hostingEnvironment = hostingEnvironment;
        }


        public string Index()
        {
            //sWCustomersUrl ==> sphere Widget customer url
            string pathBase = _httpContextAccessor.HttpContext.Request.PathBase;
            // pathBase = "/dev/widgets/offers/v1/";
            string myFullUrl = _httpContextAccessor.HttpContext.Request.Scheme + Uri.SchemeDelimiter + _httpContextAccessor.HttpContext.Request.Host + pathBase;
            //string 

            _logger.LogDebug(0, $"route widget-js: pathBase={pathBase}, myFullUrl={myFullUrl}");


            // _httpContextAccessor.HttpContext.Request.PathBase
            string readText = $"var sWOffersUrl = '{myFullUrl}'; ";

            //Récupère le chemin physique du fichier JS  widget.js
            string widgetJsPath = Path.Combine(_hostingEnvironment.WebRootPath, "js/widget.js");
            //Lit le fichier widget.js
            readText += Environment.NewLine + System.IO.File.ReadAllText(widgetJsPath);

            return MinifierJS(readText);
        }

        private static string MinifierJS(string jsContent)
        {
            var minifier = Uglify.Js(jsContent);
            return minifier.Code;
        }
    }
}
