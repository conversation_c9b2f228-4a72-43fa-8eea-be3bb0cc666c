﻿



IF OBJECT_ID (N'adhesion_adherent', N'U') IS NOT NULL 
BEGIN

	SELECT top 1 identiteDoss.identite_id as client_numero, identiteDoss.Identite_nom + ' ' + identiteDoss.identite_prenom as client_nom,

	case WHEN link_identite_adherent.adherent_id is null then '0' else link_identite_adherent.adherent_id end as adherent_num,


	CASE when id3.Identite_nom + id3.identite_prenom <>'' 
	THEN
		id3.Identite_nom + ' ' + id3.identite_prenom /* nom de l'identite liée par Entree_Complement */
	else
		CASE when con2.name + con2.surname <>'' 
		 THEN con2.name + ' ' + con2.surname /* nom de consumers liée par Entree_Complement ?!? */
		  ELSE 
		  CASE when con.name + con.surname <>'' 
		  THEN con.name + ' ' + con.surname  /* nom de consumers liée par dossier_consommateur */
			ELSE identiteDoss.Identite_nom + ' '+ identiteDoss.identite_prenom 
			END /* nom de l'acheteur */
		END end
		
	 as utilisateur_nom,
	 i.Identite_nom + ' '+ i.identite_prenom as payeur_nom, 
	 i.ref_compta, i.postal_tel5,  
	 i.postal_rue1 as client_adresse1, 
	 i.postal_rue2 as client_adresse2, i.postal_cp as client_cp, i.postal_ville as client_ville, i.postal_pays,i.postal_rue1  
	FROM identite i
	INNER JOIN commande c  on c.identite_id=i.identite_id  
	LEFT OUTER JOIN commande_ligne cl ON cl.commande_id=c.commande_id  and cl.dossier_id=@pdossierId
	LEFT OUTER JOIN identite identiteDoss on identiteDoss.identite_id = cl.identite_id 

	LEFT OUTER JOIN adhesion_adherent link_identite_adherent ON identiteDoss.identite_id = link_identite_adherent.identite_id

	LEFT OUTER JOIN dossier_consommateur dc  on dc.commande_id=c.commande_id and dc.dossier_id=@pdossierId
	LEFT OUTER JOIN consumers con on con.consumer_id=dc.consumer_id 

	LEFT OUTER JOIN Entree_Complement ec on ec.Identite_id = i.identite_id  and ec.dossier_id=@pdossierId and ec.Entree_id=@pseatId and ec.identite_id=@pIdentityId
	LEFT OUTER JOIN consumers con2 on con2.consumer_id=ec.Consommateur_ID 

	LEFT OUTER JOIN Entree_Complement ec3 on ec3.Identite_id = i.identite_id  and ec3.dossier_id=@pdossierId and ec3.Entree_id=@pseatId

	LEFT OUTER JOIN identite id3 on id3.identite_id=ec3.Consommateur_ID 


	 WHERE i.identite_ID =@pIdentityId and c.commande_id=@porderId

end
else
begin

	SELECT top 1 identiteDoss.identite_id as client_numero, identiteDoss.Identite_nom + ' ' + identiteDoss.identite_prenom as client_nom,

	'' as adherent_id,


	CASE when id3.Identite_nom + id3.identite_prenom <>'' 
	THEN
		id3.Identite_nom + ' ' + id3.identite_prenom /* nom de l'identite liée par Entree_Complement */
	else
		CASE when con2.name + con2.surname <>'' 
		 THEN con2.name + ' ' + con2.surname /* nom de consumers liée par Entree_Complement ?!? */
		  ELSE 
		  CASE when con.name + con.surname <>'' 
		  THEN con.name + ' ' + con.surname  /* nom de consumers liée par dossier_consommateur */
			ELSE identiteDoss.Identite_nom + ' '+ identiteDoss.identite_prenom 
			END /* nom de l'acheteur */
		END end
		
	 as utilisateur_nom,
	 i.Identite_nom + ' '+ i.identite_prenom as payeur_nom, 
	 i.ref_compta, i.postal_tel5,  
	 i.postal_rue1 as client_adresse1, 
	 i.postal_rue2 as client_adresse2, i.postal_cp as client_cp, i.postal_ville as client_ville, i.postal_pays,i.postal_rue1  
	FROM identite i
	INNER JOIN commande c  on c.identite_id=i.identite_id  
	LEFT OUTER JOIN commande_ligne cl ON cl.commande_id=c.commande_id  and cl.dossier_id=@pdossierId
	LEFT OUTER JOIN identite identiteDoss on identiteDoss.identite_id = cl.identite_id 


	LEFT OUTER JOIN dossier_consommateur dc  on dc.commande_id=c.commande_id and dc.dossier_id=@pdossierId
	LEFT OUTER JOIN consumers con on con.consumer_id=dc.consumer_id 

	LEFT OUTER JOIN Entree_Complement ec on ec.Identite_id = i.identite_id  and ec.dossier_id=@pdossierId and ec.Entree_id=@pseatId and ec.identite_id=@pIdentityId
	LEFT OUTER JOIN consumers con2 on con2.consumer_id=ec.Consommateur_ID 

	LEFT OUTER JOIN Entree_Complement ec3 on ec3.Identite_id = i.identite_id  and ec3.dossier_id=@pdossierId and ec3.Entree_id=@pseatId

	LEFT OUTER JOIN identite id3 on id3.identite_id=ec3.Consommateur_ID 


	 WHERE i.identite_ID =@pIdentityId and c.commande_id=@porderId




end