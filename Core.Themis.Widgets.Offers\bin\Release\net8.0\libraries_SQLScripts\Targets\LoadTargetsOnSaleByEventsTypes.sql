/*
DECLARE @pEventsTypesId varchar(max)
*/



	SELECT distinct c.* FROM Cible c
	LEFT OUTER JOIN Seance_Cible sc on c.id = sc.Cible_id
	INNER JOIN seance s ON s.seance_Id = sc.Seance_id 
	INNER JOIN gestion_place gp ON gp.seance_id = s.seance_Id and gp.isvalide = 1
	inner join manifestation m ON m.manifestation_id = s.manifestation_id
	inner join manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
	WHERE masquer = 'N' and mg.type_evenement in (select Name from splitstring(@pEventsTypesId, ','))



/*
IF @pEventTypeId >= 0
BEGIN
	SELECT distinct c.* FROM Cible c
	LEFT OUTER JOIN Seance_Cible sc on c.id = sc.Cible_id
	INNER JOIN seance s ON s.seance_Id = sc.Seance_id 
	INNER JOIN gestion_place gp ON gp.seance_id = s.seance_Id and gp.isvalide = 1
	inner join manifestation m ON m.manifestation_id = s.manifestation_id
	inner join manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
	WHERE masquer = 'N' and mg.type_evenement = @pEventTypeId
END
ELSE 
BEGIN
	SELECT distinct c.* FROM Cible c
	LEFT OUTER JOIN Seance_Cible sc on c.id = sc.Cible_id
	INNER JOIN seance s ON s.seance_Id = sc.Seance_id 
	INNER JOIN gestion_place gp ON gp.seance_id = s.seance_Id and gp.isvalide = 1
	inner join manifestation m ON m.manifestation_id = s.manifestation_id
	inner join manifestation_groupe mg ON mg.manif_groupe_id = m.manifestation_groupe_id
	WHERE masquer = 'N' 
END*/