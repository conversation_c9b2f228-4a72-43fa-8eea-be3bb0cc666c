﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NUglify;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace Core.Themis.Widgets.Offers.Controllers
{
    [Route("widget-ressources")]
    public class WidgetShareFilesController : Controller
    {
        private readonly IWebHostEnvironment _webHost;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public WidgetShareFilesController(IWebHostEnvironment webHost, IHttpContextAccessor httpContextAccessor)
        {
            _webHost = webHost;
            _httpContextAccessor = httpContextAccessor;
        }

        [Route("widget.min.css")]
        public ActionResult MinifyWidgetCss()
        {
            try
            {
                string css = GetWidgetCssFile(true);
                return Content(css, "text/css");
            }
            catch
            {
                return NotFound();
            }
        }

        [Route("widget.css")]
        public ActionResult WidgetCss()
        {
            try
            {
                string css = GetWidgetCssFile(false);
                return Content(css, "text/css");
            }
            catch
            {
                return NotFound();
            }
        }

        [Route("widget.min.js")]
        public ActionResult MinifyWidgetJs()
        {
            try
            {
                string js = GetWidgetJsFile(true);
                return Content(js, "application/javascript");
            }
            catch
            {
                return NotFound();
            }
        }

        [Route("widget.js")]
        public ActionResult WidgetJs()
        {
            try
            {
                string js = GetWidgetJsFile();
                return Content(js, "application/javascript");
            }
            catch
            {
                return NotFound();
            }
        }

        private string GetWidgetCssFile(bool minify = false)
        {
            var widgetCss = GetContentFileFromRCL("/_content/Core.Themis.Libraries.Razor/css/widget.css");

            Regex regexImport = new(@"@import\s+url\(['""]?(?<filePath>[^'""]+)['""]?\)\s*;", RegexOptions.IgnoreCase);
            MatchCollection importFiles = regexImport.Matches(widgetCss);

            StringBuilder merginCssFile = new(widgetCss);

            foreach (Match import in importFiles)
            {
                string importCss = import.Groups["filePath"].Value;

                string importContent = GetContentFileFromRCL(importCss);

                merginCssFile.Replace(import.Value, importContent);
            }

            return minify ? MinifierCSS($"{merginCssFile}") : $"{merginCssFile}";
        }

        private string GetWidgetJsFile(bool minify = false)
        {
            string hostPath = $"{_httpContextAccessor.HttpContext.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host}{_httpContextAccessor.HttpContext.Request.PathBase}";

            string widgetJsFile = GetContentFileFromRCL("/_content/Core.Themis.Libraries.Razor/js/widget.js");

            string widgetJsWithWidgetPathVariables = $"var sWAdminUrl = '{hostPath}'; {widgetJsFile}";

            return minify ? MinifierJS(widgetJsWithWidgetPathVariables) : widgetJsWithWidgetPathVariables;
        }

        private string GetContentFileFromRCL(string RCLFilePath)
        {
            var fileInfo = _webHost.WebRootFileProvider.GetFileInfo(RCLFilePath);

            string filePath = fileInfo.PhysicalPath;

            if (!System.IO.File.Exists(filePath))
                throw new FileNotFoundException($"{fileInfo.Name} not found");

            return System.IO.File.ReadAllText(filePath);
        }

        private static string MinifierCSS(string cssContent)
        {
            var minifier = Uglify.Css(cssContent);
            return minifier.Code;
        }

        private static string MinifierJS(string jsContent)
        {
            var minifier = Uglify.Js(jsContent);
            return minifier.Code;
        }
    }
}
