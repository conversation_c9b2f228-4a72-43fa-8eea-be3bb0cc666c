

select *
 from HomeModular_BlockType hmbt
inner join HomeModular_BlockFunction hmbf on hmbf.BlockType_ID = hmbt.BlockType_ID
left outer join HomeModular_Functions hmf on hmf.Functions_ID = hmbf.Functions_ID
order by BlockType_Order, BlockType_NAME, BlockFunction_order


/*
select hmbt.BlockType_ID, BlockType_Weight, BlockType_NAME, BlockFunction_Mandatory, hmbf.Functions_ID, Functions_NAME  from HomeModular_BlockType hmbt
inner join HomeModular_BlockFunction hmbf on hmbf.BlockType_ID = hmbt.BlockType_ID
left outer join HomeModular_Functions hmf on hmf.Functions_ID = hmbf.Functions_ID
order by BlockType_Order, BlockType_NAME, BlockFunction_order
*/

