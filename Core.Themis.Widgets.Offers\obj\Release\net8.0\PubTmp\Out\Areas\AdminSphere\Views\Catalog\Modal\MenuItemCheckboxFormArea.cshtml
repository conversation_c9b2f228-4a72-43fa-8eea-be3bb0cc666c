@using Core.Themis.Libraries.Razor.Areas.AdminSphere.ViewModels.Catalog
@model IEnumerable<Items>

@foreach (var (item, i) in Model.Select((item, i) => (item, i)))
{
    <div hidden="@(Model.Count() <= 1)" class="form-check form-switch">
    <input readonly hidden type="text" name="@($"Items[{i}].Id")" id="<EMAIL>" value="@item.Id">
    <input readonly hidden type="text" name="@($"Items[{i}].Name")" id="<EMAIL>" value="@item.Name">
    <input readonly hidden type="text" name="@($"Items[{i}].MainMenuType")" id="<EMAIL>" value="@item.MainMenuType">
    <input type="checkbox" checked="@item.ToCreate" class="form-check-input" name="@($"Items[{i}].ToCreate")" id="<EMAIL>">
    <label class="form-check-label" for="<EMAIL>">@Html.Raw(item.Name)</label>
</div>
}