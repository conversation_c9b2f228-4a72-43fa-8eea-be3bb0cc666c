{
  "Mode": "Dev",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    }
  },
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    //"GlobalOpinionDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SphereWebTest;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },
  "SitePaiement": "https://payment.themisweb.fr/dev/payment/optionspayment/[structureid]/[basketid]/[platformname]/[lang]/[haskey]/[identiteid]",
  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "CrossSelling": [ "Global", "CrossSelling" ],
    "Session": [ "Global", "Session" ],
    "Basket": [ "Global", "Basket" ],
    "Product": [ "Global", "Product" ],
    "HomeModular": [ "Global", "HomeModular" ],
    "Insurance": [ "Global", "Insurance" ],
    "Catalog": [ "Global", "Catalog" ]
  },
  "Cache": {
    //Cache pour la liste des manifestation en secondes
    //"EventsAbsoluteExpiration": 10,
    //"EventsSlidingExpiration": 2,
    //Cache pour la liste des seances (calendrier) en secondes
    //"SessionsAbsoluteExpiration": 10,
    //"SessionsSlidingExpiration": 2,
    //Cache de la Grille de tarif � chaque changement de zone, etage, section en secondes
    "GrilleTarifAbsoluteExpiration": 10,
    "GrilleTarifSlidingExpiration": 2,
    "InsuranceAbsoluteExpiration": 3,
    "TranslationsAbsoluteExpiration": 3,
    "SponsorsAbsoluteExpiration": 10,
    "HomeModularAbsoluteExpiration": 5,

    "AdhesionsAbsoluteExpiration": 120,
    "AdhesionsSlidingExpiration": 2,

    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 2,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 2,
    "ProductsGlobauxToBasket": 2400,
    "ProductsForEvents": 2400,
    "ProductsForSessions": 2400,

    //Cache pour la liste des manifestations (eventsCatalog) en secondes
    "EventsCatalogSlidingExpiration": 600,
    "EventsCatalogAbsoluteExpiration": 2400,
    //Cache pour la liste des manifestations (eventsCatalog) au niveau des filtres en secondes
    "EventsCatalogAFiltersbsoluteExpiration": 600,
    "EventsCatalogFilterSlidingExpiration": 2400,
    "UpdateCacheFile": "\\\\Srv-paiement64\\CUSTOMERFILES\\TEST\\updatecache.txt"
  },
  "HomeModular": {
    "Products_All_Url": "https://dev.{domain}/indiv/BoutiqueHome.aspx?idstructure={structureId}",
    "Products_Family_Url": "https://dev.{domain}/indiv/boutiqueFamille.aspx?idstructure={structureId}&fid={familyId}",
    "Products_Subfamily_Url": "https://dev.{domain}/indiv/boutiqueSousFamille.aspx?idstructure={structureId}&sfid={subfamilyId}",
    "Product_One_Url": "https://dev.{domain}/indiv/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "Events_All_Url": "https://dev.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}",
    "Events_Genre_Url": "https://dev.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&genreid={genreId}",
    "Events_Subgenre_Url": "https://dev.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&subgenreid={subgenreId}",
    "Events_Group_Url": "https://dev.{domain}/indiv/fListeManifs.aspx?idstructure={structureId}&groupid={groupId}",
    "Event_One_Url": "https://dev.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}&sessionid={sessionId}",
    "Subscription_Url": "https://dev.{domain}/abo/home/<USER>/{langCode}",
    "Events_Featured_Url": "https://dev.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",
    "Products_Featured_Url": "https://dev.{domain}/indiv/boutiqueProduitDetailWidget.aspx?idstructure={structureId}&pid={productId}",
    "CustomerArea_Url": "https://dev.{domain}/customer/home.aspx?idstructure={structureId}"
  },

  "EventsCatalog": {
    "Event_One_Url": "https://dev.{domain}/indiv/fEventChoiceIsMade.aspx?idstructure={structureId}&idevent={eventId}",

    "Abo_One_Url": "https://dev.{domain}/indiv/fChoixSeanceAbo.aspx?idstructure={structureId}&FormulaId={formulaId}",
    "WaitingList_Url": "https://dev.{domain}/customer/WaitList.aspx?idstructure={structureId}&page=waitlist&EventId={eventId}&disconnect=0&iswidget=1"
  },
  "WidgetCatalogUrl": "https://dev2.themisweb.fr/widgets/catalog/v1/",
  "WidgetOfferUrl": "https://dev2.themisweb.fr/widgets/offers/v1/",
  "WidgetCustomerUrl": "https://dev2.themisweb.fr/widgets/customers/v1/",


  "ApiAuthenticationUrl": "http://**************/dev/api_authentication/v1/api/",
  "ApiAuthenticationUrlLOCAL": "https://localhost:7022/api/",

  //"identiteSalt": "RodWebShop95",
  "identiteSalt": "none",

  "TypeRun": "TEST",
  //"PathScriptSqlCommons": "LIBRARIES_SQLSCRIPTS\\[directory\\][filename][.structureid].sql",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\{structureId}\\CONFIGSERVER\\config.ini.xml",

  "EventsImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/manifestations/",
  "RodriguePartnerName": "RODRIGUE",

  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "AudienceToken": "ThemisAPI",
  "CryptoKey": "RodWebShop95",
  "PathBase": "/widgets/offers/v1/",
  "SeatPlan": {
    "SeatingPlanPerspectivePhysicalPath": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\seatingplans\\iindexToXY_perspect[param].xml",
    "SeatingPlanUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/seatingplans/"
  },

  "Images": {
    "PanoUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/pano/",
    "BaseImagesPhysicalPath": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\",
    "EventsImagesPhysicalPath": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\manifestations\\",
    "EventsImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/manifestations/",
    "ProductsImagesPhysicalPath": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\IMAGES\\produits\\",
    "ProductsImagesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/images/produits/",
    "BaseImagesUrlPath": "/files/{structureId}/INDIV/images/",
    "AssetsImagesUrlPath": "https://dev.themisweb.fr/assets/IMAGES/indiv/"
  },

  "AssetsUrlPath": "https://dev.themisweb.fr/assets/",
  "PanoFileVersion": "pano1.20.9.js",
  "physicalPathOfSettingsJSON": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}{plateformCode}\\appsettings.json",
  "physicalPathOfInfosCompJSON": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\infoComps.json",
  "CustomerfilesIndivPhysicalPath": "\\\\**************\\CUSTOMERFILES\\DEV\\{structureId}\\INDIV\\",
  "CustomerfilesUrlPath": "https://dev.themisweb.fr/files/{structureId}/INDIV/",
  "Sponsor": {
    "SponsorAdvantageCardSalt": "7f5a54f374604d362034ba883d9ac38dc4a8f00f8dc708138135d242457a4e49"
  },
  "ConsumerCreateJsonPhysicalPath": "\\\\**************\\customerfiles\\DEV\\{structureId}\\CUSTOMER\\IHM\\addLinkedConsumerform.json",
  "physicalPathOfCountries": "\\\\**************\\customerfiles\\DEV\\{structureId}\\customer\\filesinfos\\[fileName]",
  "physicalPathOfSettingsCustomerJSON": "\\\\**************\\customerfiles\\DEV\\{structureId}\\customer\\APPSETINGS\\appsettings.json",
  "DetailedErrors": true
}
