/*
declare @pfiliereid int =  5650004
declare @plangCode varchar(2) ='fr'
*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT p.produit_id as ProductID, 
case when tp.produit_nom IS null then p.produit_nom else tp.produit_nom end as productName  , 

CONVERT(INTEGER,(montant1 + montant2)*100) as TotalAmount ,CONVERT(INTEGER,montant2 * 100)  as Charge,
isnull(tva.tva_libelle,'') as tva_libelle , isnull(tva.tva_taux,0) as tva_taux,
ps.seance_id as SessionID, ps.manifestation_id as EventID, p.produit_type
,p.pref_affichage, 1 as nbr
FROM produit p
LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LgId
LEFT OUTER JOIN tva ON  tva.tva_id = p.tva1
INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
INNER JOIN filieres_droits2 fd ON fd.droit_id=p.produit_id AND fd.nom='PRODUIT' and fd.valeur='O'
WHERE p.groupe_id=1 AND p.internet=1 and filiere_id = @pfiliereid 