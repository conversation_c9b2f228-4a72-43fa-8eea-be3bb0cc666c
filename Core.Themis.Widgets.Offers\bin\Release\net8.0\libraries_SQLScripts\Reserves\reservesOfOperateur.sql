﻿DECLARE @myreserves table (reserve_id int, reserve_nom varchar(50))
INSERT into @myreserves 
	SELECT droit_table_id, r.reserve_nom FROM operateur_droit od
		INNER JOIN reserve r on r.reserve_id  = od.droit_table_id
		WHERE droit_table ='RESERVE' and droit_valeur=1
		and od.operateur_id in ({listoperateursId})		
	UNION 
		SELECT 0 ,'AUCUNE'

SELECT reserve_id as reserveId, reserve_nom as reserveName from @myreserves order by reserve_nom
