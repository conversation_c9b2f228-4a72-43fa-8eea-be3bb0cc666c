﻿--DECLARE @pProductFamilyIds varchar(50) = '4'
--DECLARE @pLangCode VARCHAR(5) = 'fr'

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

SELECT DISTINCT 
psf.Produit_Sous_Famille_ID AS Id, 
IIF(t.Traduction = '' OR t.Traduction IS NULL, psf.Produit_Sous_Famille_Nom , t.traduction Collate database_default) AS Name
FROM produit p
INNER JOIN Produit_Lien_Sous_Famille plsf ON plsf.produit_id = p.produit_id
INNER JOIN Produit_Famille pf ON pf.Produit_Famille_ID = plsf.Produit_Famille_ID
INNER JOIN Produit_Sous_Famille psf ON pf.Produit_Famille_ID = psf.Produit_Famille_ID
LEFT JOIN traduction_langue t on t.traduction_id = psf.TraductionNom_id AND t.langue_id = @langueId
WHERE p.internet = 1
AND pf.Produit_Famille_ID IN (SELECT name FROM splitstring(@pProductFamilyIds,','))
AND pf.Masquer = 0
AND psf.Masquer = 0
