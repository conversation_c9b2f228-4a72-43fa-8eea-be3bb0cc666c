﻿using Core.Themis.Libraries.BLL.CarnetTickets;
using Core.Themis.Libraries.BLL.CarnetTickets.Interfaces;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Adhesion.Interface;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.DTO.CarnetTickets;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.CustomErrors;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class FeedBookController : Controller
    {
        private static readonly RodrigueNLogger _logger = new RodrigueNLogger();

        private readonly IMemoryCache _memoryCache;
        private readonly IAdhesionCatalogRepository _adhesionCatalogRepository;
        private readonly IConfiguration _configuration;
        //private readonly IIdentityRepository _identityRepository;
        private readonly ITranslateManager _translateManager;
        private readonly IBasketRepository _basketRepository;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IFeedBooksManager _feedbookManager;
        public FeedBookController(
            IMemoryCache memoryCache,
            IConfiguration configuration,
            IFeedBooksManager feedBooksManager,
        //IAdhesionCatalogRepository adhesionCatalogRepository,
        //  IIdentityRepository identityRepository,
        IHttpContextAccessor httpContextAccessor,
        IBasketRepository basketRepository,
                        ITranslateManager translateManager,
        IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _feedbookManager = feedBooksManager;
            //_adhesionCatalogRepository = adhesionCatalogRepository;
            //_identityRepository = identityRepository;
            _translateManager = translateManager;
            _httpContextAccessor = httpContextAccessor;
            _basketRepository = basketRepository;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
        }

        [HttpPost]
        [Route("GetFeedBookFormAjax/{structureId}/{identiteId}/{eventId}/{sessionId}/{langCode}/{webUserId}/{buyerProfilId}")]
        public async Task<IActionResult> GetFeedBookTokenFormAjax(int structureId, int identiteId, int eventId, int sessionId, string langCode,
            int webUserId, int buyerProfilId, string token,
            List<PropertiesSeatsSelectionLocationToFlag> propertiesSeatsSelectionLocationsToFlag)
        {
            var translationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode, new string[] { "Global", "Session" });

            ViewBag.PropertiesSeatsSelectionLocationsToFlag = propertiesSeatsSelectionLocationsToFlag;
            ViewBag.TranslationsList = translationsList;
            ViewBag.DeviseCode = WidgetUtilitiesHelper.GetDeviseCode(structureId);
            ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;
            ViewBag.WCustomerUrl = _configuration["WidgetCustomerUrl"].ToString();
            ViewBag.StructureId = structureId;
            ViewBag.EventId = eventId;
            ViewBag.SessionId = sessionId;
            ViewBag.IdentityId = identiteId;
            ViewBag.LangCode = langCode;
            ViewBag.WebUserId = webUserId;
            ViewBag.BuyerProfilId = buyerProfilId;
            ViewBag.PartnerToken = token;

            return PartialView("_FeedBookForm", null);

        }

        //[HttpPost]
        //[Route("CheckFeedBookAjaxFake/{structureId}")]
        //public async Task<IActionResult> CheckFeedBookAjaxFake(int structureId, string feedbookNumber, string SelectionNeedFeedBook, List<string> feedbookTokensUsed)
        //{
        //    //int l = SelectionNeedFeedBook.Length;

        //    var tbl = JsonSerializer.Deserialize<List<demandsGp>>(SelectionNeedFeedBook);
        //    _logger.Debug(structureId, $"control of feedbook {feedbookNumber}, {SelectionNeedFeedBook}...");


        //    //////// code fake pour renvoyer une réponse au js
        //    if (feedbookNumber.StartsWith("ok"))
        //    {
        //        int.TryParse(feedbookNumber.Replace("ok", ""), out int nbrOkFake);

        //        if (nbrOkFake == 0)
        //        {
        //            nbrOkFake = 999;
        //        }

        //        FeedBookDTO feedBook = new FeedBookDTO();

        //        List<responseGp> carnet = new();

        //        foreach (var dmd in tbl)
        //        {
        //            int gpId = dmd.gestionPlaceId;
        //            for (int i = 0; i < dmd.seatCount; i++)
        //            {
        //                string jetonOk = "";
        //                Random random = new Random();
        //                const string chars = "ABCDEFGH0123456789";
        //                if (i < nbrOkFake)
        //                {
        //                    jetonOk = new string(Enumerable.Repeat(chars, 10)
        //                        .Select(s => s[random.Next(s.Length)]).ToArray());
        //                }
        //                carnet.Add(new responseGp
        //                {
        //                    gestionPlaceId = dmd.gestionPlaceId,
        //                    tokenJeton = jetonOk
        //                });
        //            }
        //        }

        //        var result = new
        //        {
        //            Detail = carnet,
        //            StatusCode = StatusCodes.Status200OK,
        //        };

        //        return Ok(result);
        //    }

        //    return Problem(":(");

        //}

        [HttpGet]
        [Route("GetFeedBookAjax/{structureId}")]
        public async Task<IActionResult> GetFeedBookAjax(int structureId, string feedbookNumber)
        {
            //int l = SelectionNeedFeedBook.Length;


            _logger.Debug(structureId, $"get feedbook {feedbookNumber}...");

            var carnet = _feedbookManager.FindByIdentifiant(structureId, feedbookNumber);

            carnet = _feedbookManager.FillTokensUsed(structureId, carnet);

            var result = new
            {
                Detail = carnet,
                StatusCode = StatusCodes.Status200OK,
            };

            return Ok(result);


        }

        [HttpPost]
        [Route("CheckFeedBookAjax/{structureId}")]
        public async Task<IActionResult> CheckFeedBookAjax(int structureId, string feedbookNumber, string SelectionNeedFeedBook, string feedbookTokensUsed)
        {
            //int l = SelectionNeedFeedBook.Length;

            //List<FeedBookTokenDTO> feedbookTokensUsed2 = new List<FeedBookTokenDTO>();

            var feedbookTokenUserDto = JsonSerializer.Deserialize<List<FeedBookTokenDTO>>(feedbookTokensUsed);


            var tbl = JsonSerializer.Deserialize<List<demandsGp>>(SelectionNeedFeedBook);
            _logger.Debug(structureId, $"control of feedbook {feedbookNumber}, {SelectionNeedFeedBook}...");

            try
            {

                var ltoreturn = _feedbookManager.Attribue_TokenToGps(structureId, feedbookNumber, tbl, feedbookTokenUserDto);

                var result = new
                {
                    Detail = ltoreturn,
                    StatusCode = StatusCodes.Status200OK,
                };

                bool jetonAttribue = ltoreturn.Where(j => j.tokenId > 0).Count() > 0;
                if (!jetonAttribue) // n'a pas trouvé de jeton valide pour la demande
                {
                    result = new
                    {
                        Detail = ltoreturn,
                        StatusCode = StatusCodes.Status204NoContent
                    };
                }

                return Ok(result);
            }
            catch (FeedBookNotFoundException ex)
            {
                return Problem($"can't retrieve {feedbookNumber} feedbook", statusCode: StatusCodes.Status404NotFound);
            }
            catch (Exception ex)
            {
                return Problem(":(");
            }


            //return Problem(":(");

        }

        public class responseGp
        {
            public int gestionPlaceId { get; set; }
            public string tokenJeton { get; set; }
        }


    }
}
