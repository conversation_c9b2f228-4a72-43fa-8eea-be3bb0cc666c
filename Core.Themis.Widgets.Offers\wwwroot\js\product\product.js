﻿$(document).ready(function () {
    sendIframeSize()
});

// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
// (custom pour la page "Events")
function lessReady() {
    sendIframeSize()
    console.log('product.js lessReady READY')

}


function productAddedToBasket(structureId, basketId, hash) {
    var msg = {
        "action": "basketUpdated",
        "structureId": structureId,
        "basketId": basketId,
        "hash": hash
    }
    window.parent.postMessage(msg, '*')
}