-- Type de manif
--Get cible
/*
DECLARE @pEventsTypesId varchar(max) ='3'
DECLARE @pLangCode varchar(5) = 'fr'
*/

DECLARE @langueId int = (SELECT langue_id FROM langue where langue_code = @pLangCode)

CREATE TABLE #myTargets
(
	Id INT,
	Name VARCHAR(200)
)

DECLARE @traductionCibleExist INT  = (SELECT count(*) FROM sys.views WHERE name like 'traduction_cible')

IF @traductionCibleExist = 0
BEGIN
	INSERT INTO #myTargets
	SELECT DISTINCT c.id as Id, c.nom AS Name 
	FROM manifestation m 
	INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
	INNER JOIN Seance_Cible sc ON sc.Seance_id = s.seance_Id
	INNER JOIN Cible c ON c.id = sc.Cible_id
	INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id 
	WHERE mgr.type_evenement in (SELECT name FROM splitstring(@pEventsTypesId,','))
	AND mgr.supprimer = 'N'
	AND GETDATE() < ANY (SELECT s.seance_date_deb FROM seance s WHERE s.manifestation_id = m.manifestation_id)
END
ELSE
BEGIN
	INSERT INTO #myTargets
	SELECT DISTINCT c.id as Id, ISNULL(tc.nom, c.nom) AS Name 
	FROM manifestation m 
	INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
	INNER JOIN Seance_Cible sc ON sc.Seance_id = s.seance_Id
	INNER JOIN Cible c ON c.id = sc.Cible_id
	LEFT OUTER JOIN traduction_cible tc ON tc.id = c.id AND tc.langue_id = @langueId
	INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id 
	WHERE mgr.type_evenement in (SELECT name FROM splitstring(@pEventsTypesId,','))
	AND mgr.supprimer = 'N'
	AND GETDATE() < ANY (SELECT s.seance_date_deb FROM seance s WHERE s.manifestation_id = m.manifestation_id)
END

IF((SELECT COUNT(DISTINCT m.manifestation_id)
	FROM manifestation m 
	INNER JOIN seance s ON s.manifestation_id = m.manifestation_id
	WHERE NOT EXISTS(SELECT * FROM Seance_Cible WHERE Seance_id = s.seance_Id)
	AND GETDATE() < ANY (SELECT s_any.seance_date_deb FROM seance s_any WHERE s_any.manifestation_id = m.manifestation_id)) > 0
)
BEGIN
	INSERT INTO #myTargets VALUES (0, 'Autres')
END

SELECT * FROM #myTargets

DROP TABLE #myTargets


