
/*
Declare @DossierId int
Declare @EntreeId int
Declare @SeanceId int
*/

Declare @ManifId int

Declare @numCodeBarre_externe VARCHAR(50)
Declare @numCodeBarre_motif VARCHAR(50)

IF (OBJECT_ID (N'recette_histo_updatesExternes', N'U') IS NULL)
begin
	THROW 51000, 'The table recette_histo_updatesExternes does not exist.', 1;
end

SELECT @ManifId = manifestation_id FROM Commande_ligne_comp WHERE seance_id = @SeanceId AND dossier_id = @DossierId
if (@ManifId is null)
begin
	declare @msg varchar(100) = 'Can''t find event for session ' + convert(varchar(50), @seanceId) + ' and dossier ' + convert(varchar(50),@DossierId);
		
	THROW 51000, @msg, 1;
end

	SELECT @numCodeBarre_externe =  ISNULL(externe, '') FROM recette WHERE  seance_id = @SeanceId AND dossier_id = @DossierId AND entree_id = @EntreeId
	SELECT @numCodeBarre_motif =  ISNULL(motif, '') FROM recette WHERE  seance_id = @SeanceId AND dossier_id = @DossierId AND entree_id = @EntreeId

--select* from recette order by recette_id desc
BEGIN TRANSACTION

BEGIN TRY

	SELECT @numCodeBarre_externe =  ISNULL(externe, '') FROM recette WHERE  seance_id = @SeanceId AND dossier_id = @DossierId AND entree_id = @EntreeId
	SELECT @numCodeBarre_motif =  ISNULL(motif, '') FROM recette WHERE  seance_id = @SeanceId AND dossier_id = @DossierId AND entree_id = @EntreeId

	IF @numCodeBarre_externe <> ''
	BEGIN

		INSERT INTO recette_histo_updatesExternes (recette_id, numbillet, old_motif, old_externe, externe, origine, partner_name)
		SELECT recette_id, 
			   numbillet, 
			   motif, 
			   externe, 
			   @partner + '-'  + @numCodeBarre_externe, 
			   @numCodeBarre_externe,
			   @partner
		FROM recette 
		WHERE  externe = @numCodeBarre_externe 

		UPDATE recette SET externe =   @partner + '-'  + @numCodeBarre_externe , numbillet = 0
		FROM recette r 
		WHERE externe =  @numCodeBarre_externe


		UPDATE recette_complement SET codebarre =   @partner + '-'  + codebarre
		FROM recette_complement rc 
		WHERE codebarre =  @numCodeBarre_externe

	   	 	
	END
	ELSE IF @numCodeBarre_motif <> ''
	BEGIN
		

 		INSERT INTO recette_histo_updatesExternes (recette_id, numbillet, old_motif, old_externe, externe, origine, partner_name)
		SELECT recette_id, 
			   numbillet, 
			   motif, 
			   externe, 
			   @partner + '-'  + @numCodeBarre_motif, 
			   @numCodeBarre_motif,
			   @partner
		FROM recette 
		WHERE  motif = @numCodeBarre_motif 

		UPDATE recette SET motif =   @partner + '-'  + @numCodeBarre_motif , numbillet = 0
		FROM recette r 
		WHERE motif =  @numCodeBarre_motif

		UPDATE recette_complement SET codebarre =   @partner + '-'  + codebarre
		FROM recette_complement rc 
		WHERE codebarre =  @numCodeBarre_motif

	END	


	SELECT @ManifId = manifestation_id FROM Commande_ligne_comp WHERE seance_id = @SeanceId AND dossier_id = @DossierId

	Declare @sql nvarchar(max) = '
 
	/* controle acces sur entree_xx */
	 UPDATE entree_'+ convert(varchar,@ManifId)+ ' SET controleacces=''9'' WHERE entree_etat IN (''P'',''B'') AND entree_id IN (@myentreeId);
   
	 /* comm sur dossier_xx */
	 UPDATE dossier_'+ convert(varchar,@ManifId)+ ' SET dossier_c = ''Sinistre déclaré. Assurance annulation utilisée.'' 
	 FROM dossier_'+ convert(varchar,@ManifId)+ ' d INNER JOIN entree_'+ convert(varchar,@ManifId)+ ' e
	 ON e.dossier_id = d.dossier_id WHERE dossier_etat IN (''P'', ''B'') AND entree_id =@myentreeId AND d.dossier_id=@mydossierId
	'

	exec sp_executesql @sql,N'@myentreeId int, @mydossierId int', @myentreeId=@EntreeId , @mydossierId=@DossierId
	COMMIT

END TRY
BEGIN CATCH

	select 'error'
	ROLLBACK;
	 THROW; 
END CATCH