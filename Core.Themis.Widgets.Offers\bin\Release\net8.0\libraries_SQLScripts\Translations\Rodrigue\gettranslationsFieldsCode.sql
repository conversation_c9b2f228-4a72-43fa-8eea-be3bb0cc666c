﻿/*
declare @pArea varchar(50)
set @pArea = 'OpinionOrderAnalytics,Global'
*/


if @pArea <> ''
begin
	select ta.id, ta.name, tfc.id, fieldSpecificCode, tfc.description, area_id, global_field_id 
	from translate_fieldsCodesList tfc 
	inner join translate_areas ta on tfc.area_id = ta.id 
	where ta.name in (SELECT Name FROM splitstring(@pArea, ','))
end
else
Begin
	select ta.id, ta.name, tfc.id, fieldSpecificCode, tfc.description, area_id, global_field_id 
	from translate_fieldsCodesList tfc 
	inner join translate_areas ta on tfc.area_id = ta.id 

end



--select id, fieldSpecificCode, description, area_id, global_field_id from translate_fieldsCodesList
