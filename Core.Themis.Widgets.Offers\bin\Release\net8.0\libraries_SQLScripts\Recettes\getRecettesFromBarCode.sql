 /* 
 
declare @pColEmail int = 6

declare @pbarCode varchar(100)

set @pbarCode='2100054083'                                                                  
set @pbarCode='2100004092'   
set @pbarCode='4604300736'
 */


 
SELECT top 1
 idos.identite_id as identiteIdDossier,
 idos.identite_nom as identiteNomDossier,
 idos.identite_prenom as identitePrenomDossier,

 ipay.identite_id as identiteIdpay,
 ipay.identite_nom as identiteNompay,
 ipay.identite_prenom as identitePrenompay,
 case @pColEmail 
 when 1 then ipay.postal_tel1 
 when 2 then ipay.postal_tel2
 when 3 then ipay.postal_tel3
 when 4 then ipay.postal_tel4
 when 5 then ipay.postal_tel5
 when 6 then ipay.postal_tel6
 when 7 then ipay.postal_tel7

 end as identiteEmailPay,

 s.seance_date_deb,
 r.numbillet, externe, motif, 
 case when externe <> '' then externe else motif end as barcode,
 r.manifestation_id, entree_id, r.recette_id, 
 r.seance_id, 
 type_operation, date_operation
 ,ca.Id as caid, ca.datecontrole,
 r.categorie_id, cat.categ_nom,
  r.type_tarif_id, tt.type_tarif_nom,
  r.dossier_id
  
 FROM recette  r
 inner join commande_ligne cl on r.dossier_id = cl.dossier_id and r.seance_id = cl.seance_id and cl.type_ligne='DOS'
 inner join seance s on s.seance_id = cl.seance_id
 
inner join type_tarif tt on tt.type_tarif_id = r.type_tarif_id
inner join categorie cat on cat.categ_id = r.categorie_id

 inner join compte_client cc on cc.commande_id = cl.commande_id and cc.dossier_id = cl.dossier_id and cl.seance_id = cc.seance_id and cc.cc_intitule_operation = 'CREDITDOS'
inner join compte_client cc2 on cc2.cc_numpaiement = cc.cc_numpaiement and cc2.mode_paiement_id > 0 and cc2.cc_credit >0
inner join identite idos on idos.identite_id = cc.identite_id
inner join identite ipay on ipay.identite_id = cc2.identite_id

LEFT OUTER JOIN Controle_Acces ca on ca.recette_id=r.recette_id
WHERE (externe = @pbarCode and motif='') or (motif = @pbarCode and externe='')
--where idos.identite_id <> ipay.identite_id
order by r.recette_id desc

