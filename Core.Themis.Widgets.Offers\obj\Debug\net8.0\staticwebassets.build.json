{"Version": 1, "Hash": "LI+ZQiXxlqkIfzG6yKvf2biUz9zwEYFmWUzDhcOHCXk=", "Source": "Core.Themis.Widgets.Offers", "BasePath": "_content/Core.Themis.Widgets.Offers", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj", "Version": 2, "Source": "Core.Themis.Libraries.Razor", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [{"Name": "Core.Themis.Libraries.Razor\\wwwroot", "Source": "Core.Themis.Libraries.Razor", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "Pattern": "**"}, {"Name": "Core.Themis.Widgets.Offers\\wwwroot", "Source": "Core.Themis.Widgets.Offers", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "7t9tbfaemk", "Integrity": "FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.ai.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m4v3m943t1", "Integrity": "qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "63oqyoiiv4", "Integrity": "jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mgojsf1q78", "Integrity": "ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.pdf.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ct0ej5e0q0", "Integrity": "oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.sortable-list.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ezlxc6gzv3", "Integrity": "du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.theme-switcher.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pisakkcwob", "Integrity": "Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "icon/128X128.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tngynhsog2", "Integrity": "J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "pdfjs-4.0.379.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j8zp7bt7w3", "Integrity": "XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "pdfjs-4.0.379.worker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "79hai7knhw", "Integrity": "JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css", "SourceId": "BlazorDateRangePicker", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "BasePath": "_content/BlazorDateRangePicker", "RelativePath": "BlazorDateRangePicker.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "iitr8bszy4", "Integrity": "1p2vug7PF4nK8g3cCr3/Gge2gk18Zm5z3uBJvJgYmTc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js", "SourceId": "BlazorDateRangePicker", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "BasePath": "_content/BlazorDateRangePicker", "RelativePath": "clickAndPositionHandler.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lywn4it7vj", "Integrity": "Y13FGi0LwdilZTDiSJhwO9kI/dHRax0/H52S+Yq1Ae0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Inputs/InputImageFileCustom.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rdyabvu3r3", "Integrity": "ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Inputs/InputTextAreaCustom.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d5ekrygsx9", "Integrity": "oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Select/NewSelect2.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7w0wqjb5ml", "Integrity": "eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Select/Select2.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xa30f2o21h", "Integrity": "ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Sortable/SortableList.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p4z3zfclgx", "Integrity": "Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Core.Themis.Libraries.Razor#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "rpcwcmxhht", "Integrity": "oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "css/common#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fslu2o6p56", "Integrity": "zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "css/widget#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42ua3cf6ss", "Integrity": "rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/common_razor_library#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sdkbw9k9a5", "Integrity": "EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/phoneInput#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5yj6xghg17", "Integrity": "g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/widget#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nufmjs66fb", "Integrity": "QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Core.Themis.Widgets.Offers.styles.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Computed", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "Core.Themis.Widgets.Offers#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "xfb49vdv5o", "Integrity": "7Sdmb+e6OHt5yw6sRL/jAvf6Tbf+AWldM8nAsp/bfjs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Core.Themis.Widgets.Offers.styles.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Basket\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/Basket/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1st4<PERSON><PERSON><PERSON>", "Integrity": "wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Basket\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\catalog\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/catalog/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g4aqg6g1za", "Integrity": "wN02Zv8Pu3w2ox27gE3amYrrmis+8KWuqzIAD5vscwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\catalog\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\CrossSelling\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/CrossSelling/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vsciafzuvd", "Integrity": "sVjlBfxg6iqeF0g7ULFoytSHYY+zqSb4vtDHseikp94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\CrossSelling\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\brands.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/fontawesome/brands.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ez2xkn8945", "Integrity": "pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\fontawesome\\brands.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\fontawesome.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/fontawesome/fontawesome.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "scp72qe8l0", "Integrity": "5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\fontawesome\\fontawesome.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\solid.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/fontawesome/solid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "51zpey82mz", "Integrity": "mRMKBdbqofxkeOKFxu1cD+axDJzOeu37H7OErPSzNgw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\fontawesome\\solid.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\HomeModular\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/HomeModular/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qkc7ff5z8n", "Integrity": "lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\HomeModular\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Insurance\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/Insurance/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "140muf1qxf", "Integrity": "EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Insurance\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\ProductDetails\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/Product/ProductDetails/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gv2otlqfd8", "Integrity": "jDUiX8Ca+7parAAkqp1y+7LAhzlvZ2YNH4k8Bw5uWMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Product\\ProductDetails\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/Product/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1h25wggyzv", "Integrity": "O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Product\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Session\\style.less", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/Session/style#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "oa78z5pqen", "Integrity": "8B1xzGn8zKYinkrW3oeTtV2w1fNvDDGJLD7eNdZ0tWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Session\\style.less"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.ttf", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-brands-400#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "izvpo0fq0c", "Integrity": "Lvb93nBxk+Q3widTdVyzL7iSHd5/9nQ5tY5tyjxaC+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-brands-400.ttf"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.woff2", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-brands-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w3gmvxomfo", "Integrity": "9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-brands-400.woff2"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.ttf", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-regular-400#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i8qdkhure4", "Integrity": "Et6he2iq+z9DyoFiigGzP+1z2bvUNNvSvIUSvP8FNKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-regular-400.ttf"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.woff2", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-regular-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tiuka3971e", "Integrity": "e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-regular-400.woff2"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.ttf", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-solid-900#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xyf340uas6", "Integrity": "Z6iAtLpSm5rMMD1ym1a0/XCG+yLUJWYEELjFHheE9ik=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-solid-900.ttf"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.woff2", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-solid-900#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q5d2fvgdiy", "Integrity": "4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU+pg7kI/4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-solid-900.woff2"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.ttf", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-v4compatibility#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1rckkh26l5", "Integrity": "jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-v4compatibility.ttf"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.woff2", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "css/webfonts/fa-v4compatibility#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0bf02qslzm", "Integrity": "fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\webfonts\\fa-v4compatibility.woff2"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\favicon.ico", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\angle_button_white.svg", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "img/angle_button_white#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hoal44qztg", "Integrity": "r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\angle_button_white.svg"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\sessionnotavailable.png", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "img/sessionnotavailable#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wwx3912tbx", "Integrity": "kIRR6icIjLjhvJTiRMZcxN9T74VR8+q5HvYJnmDI0iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\sessionnotavailable.png"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\ticket-shape-bottom.svg", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "img/ticket-shape-bottom#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kbtng4gjpy", "Integrity": "MFTmQZ3+SjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\ticket-shape-bottom.svg"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\basket\\basket.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/basket/basket#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tja0fkan1w", "Integrity": "adSEjCz5rrFNzZyRA/cUsa7Thv54opWordKjrxhuF94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\basket\\basket.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\bootstrap-spinner\\bootstrap-input-spinner.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/bootstrap-spinner/bootstrap-input-spinner#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "91h9pnmz7i", "Integrity": "fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\bootstrap-spinner\\bootstrap-input-spinner.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\catalog\\catalog.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/catalog/catalog#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aybzvw0lo8", "Integrity": "Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM+QIx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\catalog\\catalog.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\categprices\\categprices.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/categprices/categprices#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eg79ao16si", "Integrity": "/sdO8jhyJ/Nql0tNPT+5r6PTcOVTp6u2OHwrL1aWNaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\categprices\\categprices.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\commons.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/commons#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h0qjbqpakw", "Integrity": "j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ+rtjV1GC0RM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\commons.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\crossSelling\\crossSelling.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/crossSelling/crossSelling#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "arhin6lv8f", "Integrity": "mnZLlcvkdD2Q1FdNxspr+KAEC3ZJa1RUOp4K2+rJMLE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\crossSelling\\crossSelling.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\feedbook\\feedbookForm.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/feedbook/feedbookForm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9simdimwy5", "Integrity": "mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\feedbook\\feedbookForm.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\homemodular\\homemodular.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/homemodular/homemodular#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pz7fifd5mo", "Integrity": "NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\homemodular\\homemodular.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\insurance\\insurance.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/insurance/insurance#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5w9vrrajao", "Integrity": "KxXX2S8dPxTQSYJi+ijA9oDxCm7CUxC2AhCHrhozgqs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\insurance\\insurance.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\lessModify.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/lessModify#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h35nv9j0tn", "Integrity": "jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\lessModify.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\product.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/product/product#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l813fa0qah", "Integrity": "BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\product\\product.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\productDetails\\productDetailsCADH.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/product/productDetails/productDetailsCADH#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0x0wh8qap2", "Integrity": "XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\product\\productDetails\\productDetailsCADH.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\seatsSelection\\seatsSelection.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/seatsSelection/seatsSelection#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y1y03gh8aq", "Integrity": "Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\seatsSelection\\seatsSelection.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\pano.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/session/pano#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lf2ncmqb44", "Integrity": "74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\session\\pano.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\seatplan.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/session/seatplan#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8w64e9o242", "Integrity": "GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz+z2hL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\session\\seatplan.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\session.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/session/session#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3lezgcpngy", "Integrity": "abskm4cXOt2hXXgjXam+ebTyM1cQh/tA1TJZhwLpsd8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\session\\session.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\site.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6b9nfc4n3r", "Integrity": "4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\tunnel\\tunnel.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/tunnel/tunnel#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sp16istu3z", "Integrity": "MODq8+xYTX9x74jt+P1qSk4v7tqc6Y9f7c2v/+evnFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\tunnel\\tunnel.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\widget.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "js/widget#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5yj8jvbnmo", "Integrity": "A6qF4CNZE8mLG516CVfmo8rso7giA09LEnGainFRT3c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\widget.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gawgt6fljy", "Integrity": "eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k9n1kkbua6", "Integrity": "CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "etnb7xlipe", "Integrity": "rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kao5znno1s", "Integrity": "xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9n0ta5ieki", "Integrity": "ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0i1dcxd824", "Integrity": "2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vxs71z90fw", "Integrity": "vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jzb7jyrjvs", "Integrity": "kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nynt4yc5xr", "Integrity": "cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tgg2bl5mrw", "Integrity": "3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "brwg1hntyu", "Integrity": "1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gf2dxac9qe", "Integrity": "dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u0biprgly9", "Integrity": "srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pxamm17y9e", "Integrity": "3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hju<PERSON><PERSON>ly30", "Integrity": "XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u9xms436mi", "Integrity": "8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "48vr37mrsy", "Integrity": "LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vaswmcjbo4", "Integrity": "R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zu238p5lxg", "Integrity": "O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sgi57dik4g", "Integrity": "vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "weyt030wr8", "Integrity": "iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vwa26bmbsk", "Integrity": "QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5trh6b1mit", "Integrity": "9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dsw5v3fbc5", "Integrity": "PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "afgyafcsqt", "Integrity": "WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wus95c49fh", "Integrity": "qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sjab29p8z5", "Integrity": "2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jdpnbaa6vo", "Integrity": "yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "59qolqi738", "Integrity": "F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0td7jq9nxb", "Integrity": "XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v12ed9ioy0", "Integrity": "4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\testingCallOffersLocal.html", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/testingCallOffersLocal#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pn1mdxt63j", "Integrity": "G626HHRkuN+sz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\testingCallOffersLocal.html"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.css", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/websiteexterne#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6vn0p36f17", "Integrity": "LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA+3rVoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\websiteexterne.css"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterne.html", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/WebSiteExterne#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2exqpgysr8", "Integrity": "ikQY9P79wVARu+344J9kh1cizTuYfzoXtvT2pzCh3j4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\WebSiteExterne.html"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.js", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/websiteexterne#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3d1vw2wx03", "Integrity": "WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB+wYMg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\websiteexterne.js"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterneCallLocal.html", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/WebSiteExterneCallLocal#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3o5my4v6zj", "Integrity": "zdf2S9P7cq/t3T18wOoppYsNJA4GMCzSOvBa+KKwsLw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\WebSiteExterneCallLocal.html"}, {"Identity": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteExterneTunnel.html", "SourceId": "Core.Themis.Widgets.Offers", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "BasePath": "_content/Core.Themis.Widgets.Offers", "RelativePath": "websiteexterneDemo/websiteExterneTunnel#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rbsndwsa29", "Integrity": "J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\websiteexterneDemo\\websiteExterneTunnel.html"}], "Endpoints": [{"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.63oqyoiiv4.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22639"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Nov 2024 14:43:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63oqyoiiv4"}, {"Name": "integrity", "Value": "sha256-jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.css"}]}, {"Route": "_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14166"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=\""}, {"Name": "Last-Modified", "Value": "Sat, 02 Nov 2024 09:45:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7t9tbfaemk"}, {"Name": "integrity", "Value": "sha256-FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/Blazor.Bootstrap.bundle.scp.css"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2000"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.m4v3m943t1.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2000"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m4v3m943t1"}, {"Name": "integrity", "Value": "sha256-qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.js"}]}, {"Route": "_content/Blazor.Bootstrap/Blazor.Bootstrap.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14166"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=\""}, {"Name": "Last-Modified", "Value": "Sat, 02 Nov 2024 09:45:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22639"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Nov 2024 14:43:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "79921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.mgojsf1q78.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "79921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mgojsf1q78"}, {"Name": "integrity", "Value": "sha256-ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.ct0ej5e0q0.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7290"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ct0ej5e0q0"}, {"Name": "integrity", "Value": "sha256-oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7290"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.ezlxc6gzv3.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Apr 2024 19:30:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ezlxc6gzv3"}, {"Name": "integrity", "Value": "sha256-du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Apr 2024 19:30:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2896"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Nov 2024 15:30:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.pisakkcwob.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2896"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Nov 2024 15:30:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pisakkcwob"}, {"Name": "integrity", "Value": "sha256-Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.js"}]}, {"Route": "_content/Blazor.Bootstrap/icon/128X128.png", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Apr 2022 11:45:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU="}]}, {"Route": "_content/Blazor.Bootstrap/icon/128X128.tngynhsog2.png", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Apr 2022 11:45:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tngynhsog2"}, {"Name": "integrity", "Value": "sha256-J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/icon/128X128.png"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.j8zp7bt7w3.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "305685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j8zp7bt7w3"}, {"Name": "integrity", "Value": "sha256-XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.js"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "305685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ="}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.79hai7knhw.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1029159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "79hai7knhw"}, {"Name": "integrity", "Value": "sha256-JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.js"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1029159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ="}]}, {"Route": "_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10857"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1p2vug7PF4nK8g3cCr3/Gge2gk18Zm5z3uBJvJgYmTc=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 15:56:12 GMT"}], "EndpointProperties": []}, {"Route": "_content/BlazorDateRangePicker/clickAndPositionHandler.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5344"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y13FGi0LwdilZTDiSJhwO9kI/dHRax0/H52S+Yq1Ae0=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 17:14:55 GMT"}], "EndpointProperties": []}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputImageFileCustom.razor.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "122"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputTextAreaCustom.razor.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Select/NewSelect2.razor.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Select/Select2.razor.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Sortable/SortableList.razor.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.bundle.scp.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rpcwcmxhht"}, {"Name": "integrity", "Value": "sha256-oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.bundle.scp.css"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/common.fslu2o6p56.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fslu2o6p56"}, {"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/css/common.less"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/common.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/widget.42ua3cf6ss.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26069"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42ua3cf6ss"}, {"Name": "integrity", "Value": "sha256-rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/css/widget.css"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/widget.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26069"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4059"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.sdkbw9k9a5.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4059"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sdkbw9k9a5"}, {"Name": "integrity", "Value": "sha256-EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.js"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/phoneInput.5yj6xghg17.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5yj6xghg17"}, {"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/phoneInput.js"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/phoneInput.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/widget.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/widget.nufmjs66fb.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nufmjs66fb"}, {"Name": "integrity", "Value": "sha256-QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/widget.js"}]}, {"Route": "Core.Themis.Widgets.Offers.styles.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Core.Themis.Widgets.Offers.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Sdmb+e6OHt5yw6sRL/jAvf6Tbf+AWldM8nAsp/bfjs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:57 GMT"}, {"Name": "Link", "Value": "<_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7Sdmb+e6OHt5yw6sRL/jAvf6Tbf+AWldM8nAsp/bfjs="}]}, {"Route": "Core.Themis.Widgets.Offers.xfb49vdv5o.styles.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Core.Themis.Widgets.Offers.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7Sdmb+e6OHt5yw6sRL/jAvf6Tbf+AWldM8nAsp/bfjs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:08:57 GMT"}, {"Name": "Link", "Value": "<_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xfb49vdv5o"}, {"Name": "integrity", "Value": "sha256-7Sdmb+e6OHt5yw6sRL/jAvf6Tbf+AWldM8nAsp/bfjs="}, {"Name": "label", "Value": "Core.Themis.Widgets.Offers.styles.css"}]}, {"Route": "css/Basket/style.1st4pyezey.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Basket\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27507"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1st4<PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "css/Basket/style.less"}, {"Name": "integrity", "Value": "sha256-wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k="}]}, {"Route": "css/Basket/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Basket\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27507"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k="}]}, {"Route": "css/catalog/style.g4aqg6g1za.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\catalog\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11930"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wN02Zv8Pu3w2ox27gE3amYrrmis+8KWuqzIAD5vscwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g4aqg6g1za"}, {"Name": "label", "Value": "css/catalog/style.less"}, {"Name": "integrity", "Value": "sha256-wN02Zv8Pu3w2ox27gE3amYrrmis+8KWuqzIAD5vscwg="}]}, {"Route": "css/catalog/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\catalog\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11930"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wN02Zv8Pu3w2ox27gE3amYrrmis+8KWuqzIAD5vscwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wN02Zv8Pu3w2ox27gE3amYrrmis+8KWuqzIAD5vscwg="}]}, {"Route": "css/CrossSelling/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\CrossSelling\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6011"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sVjlBfxg6iqeF0g7ULFoytSHYY+zqSb4vtDHseikp94=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sVjlBfxg6iqeF0g7ULFoytSHYY+zqSb4vtDHseikp94="}]}, {"Route": "css/CrossSelling/style.vsciafzuvd.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\CrossSelling\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6011"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sVjlBfxg6iqeF0g7ULFoytSHYY+zqSb4vtDHseikp94=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vsciafzuvd"}, {"Name": "label", "Value": "css/CrossSelling/style.less"}, {"Name": "integrity", "Value": "sha256-sVjlBfxg6iqeF0g7ULFoytSHYY+zqSb4vtDHseikp94="}]}, {"Route": "css/fontawesome/brands.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\brands.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18663"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew="}]}, {"Route": "css/fontawesome/brands.min.ez2xkn8945.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\brands.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18663"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ez2xkn8945"}, {"Name": "label", "Value": "css/fontawesome/brands.min.css"}, {"Name": "integrity", "Value": "sha256-pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew="}]}, {"Route": "css/fontawesome/fontawesome.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\fontawesome.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc="}]}, {"Route": "css/fontawesome/fontawesome.min.scp72qe8l0.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\fontawesome.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "scp72qe8l0"}, {"Name": "label", "Value": "css/fontawesome/fontawesome.min.css"}, {"Name": "integrity", "Value": "sha256-5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc="}]}, {"Route": "css/fontawesome/solid.min.51zpey82mz.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\solid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "572"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mRMKBdbqofxkeOKFxu1cD+axDJzOeu37H7OErPSzNgw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51zpey82mz"}, {"Name": "label", "Value": "css/fontawesome/solid.min.css"}, {"Name": "integrity", "Value": "sha256-mRMKBdbqofxkeOKFxu1cD+axDJzOeu37H7OErPSzNgw="}]}, {"Route": "css/fontawesome/solid.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\fontawesome\\solid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "572"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mRMKBdbqofxkeOKFxu1cD+axDJzOeu37H7OErPSzNgw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mRMKBdbqofxkeOKFxu1cD+axDJzOeu37H7OErPSzNgw="}]}, {"Route": "css/HomeModular/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\HomeModular\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22023"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0="}]}, {"Route": "css/HomeModular/style.qkc7ff5z8n.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\HomeModular\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22023"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qkc7ff5z8n"}, {"Name": "label", "Value": "css/HomeModular/style.less"}, {"Name": "integrity", "Value": "sha256-lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0="}]}, {"Route": "css/Insurance/style.140muf1qxf.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Insurance\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9480"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "140muf1qxf"}, {"Name": "label", "Value": "css/Insurance/style.less"}, {"Name": "integrity", "Value": "sha256-EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo="}]}, {"Route": "css/Insurance/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Insurance\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9480"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo="}]}, {"Route": "css/Product/ProductDetails/style.gv2otlqfd8.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\ProductDetails\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2369"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jDUiX8Ca+7parAAkqp1y+7LAhzlvZ2YNH4k8Bw5uWMU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gv2otlqfd8"}, {"Name": "label", "Value": "css/Product/ProductDetails/style.less"}, {"Name": "integrity", "Value": "sha256-jDUiX8Ca+7parAAkqp1y+7LAhzlvZ2YNH4k8Bw5uWMU="}]}, {"Route": "css/Product/ProductDetails/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\ProductDetails\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2369"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jDUiX8Ca+7parAAkqp1y+7LAhzlvZ2YNH4k8Bw5uWMU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jDUiX8Ca+7parAAkqp1y+7LAhzlvZ2YNH4k8Bw5uWMU="}]}, {"Route": "css/Product/style.1h25wggyzv.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7928"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1h25wggyzv"}, {"Name": "label", "Value": "css/Product/style.less"}, {"Name": "integrity", "Value": "sha256-O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8="}]}, {"Route": "css/Product/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Product\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7928"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8="}]}, {"Route": "css/Session/style.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Session\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29710"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8B1xzGn8zKYinkrW3oeTtV2w1fNvDDGJLD7eNdZ0tWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 07:31:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8B1xzGn8zKYinkrW3oeTtV2w1fNvDDGJLD7eNdZ0tWE="}]}, {"Route": "css/Session/style.oa78z5pqen.less", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\Session\\style.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29710"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8B1xzGn8zKYinkrW3oeTtV2w1fNvDDGJLD7eNdZ0tWE=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 07:31:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oa78z5pqen"}, {"Name": "label", "Value": "css/Session/style.less"}, {"Name": "integrity", "Value": "sha256-8B1xzGn8zKYinkrW3oeTtV2w1fNvDDGJLD7eNdZ0tWE="}]}, {"Route": "css/webfonts/fa-brands-400.izvpo0fq0c.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "187448"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Lvb93nBxk+Q3widTdVyzL7iSHd5/9nQ5tY5tyjxaC+8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "izvpo0fq0c"}, {"Name": "label", "Value": "css/webfonts/fa-brands-400.ttf"}, {"Name": "integrity", "Value": "sha256-Lvb93nBxk+Q3widTdVyzL7iSHd5/9nQ5tY5tyjxaC+8="}]}, {"Route": "css/webfonts/fa-brands-400.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "187448"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Lvb93nBxk+Q3widTdVyzL7iSHd5/9nQ5tY5tyjxaC+8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lvb93nBxk+Q3widTdVyzL7iSHd5/9nQ5tY5tyjxaC+8="}]}, {"Route": "css/webfonts/fa-brands-400.w3gmvxomfo.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108000"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w3gmvxomfo"}, {"Name": "label", "Value": "css/webfonts/fa-brands-400.woff2"}, {"Name": "integrity", "Value": "sha256-9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0="}]}, {"Route": "css/webfonts/fa-brands-400.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108000"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0="}]}, {"Route": "css/webfonts/fa-regular-400.i8qdkhure4.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63728"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Et6he2iq+z9DyoFiigGzP+1z2bvUNNvSvIUSvP8FNKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i8qdkhure4"}, {"Name": "label", "Value": "css/webfonts/fa-regular-400.ttf"}, {"Name": "integrity", "Value": "sha256-Et6he2iq+z9DyoFiigGzP+1z2bvUNNvSvIUSvP8FNKY="}]}, {"Route": "css/webfonts/fa-regular-400.tiuka3971e.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24840"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tiuka3971e"}, {"Name": "label", "Value": "css/webfonts/fa-regular-400.woff2"}, {"Name": "integrity", "Value": "sha256-e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs="}]}, {"Route": "css/webfonts/fa-regular-400.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63728"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Et6he2iq+z9DyoFiigGzP+1z2bvUNNvSvIUSvP8FNKY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Et6he2iq+z9DyoFiigGzP+1z2bvUNNvSvIUSvP8FNKY="}]}, {"Route": "css/webfonts/fa-regular-400.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24840"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs="}]}, {"Route": "css/webfonts/fa-solid-900.q5d2fvgdiy.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "149908"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU+pg7kI/4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q5d2fvgdiy"}, {"Name": "label", "Value": "css/webfonts/fa-solid-900.woff2"}, {"Name": "integrity", "Value": "sha256-4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU+pg7kI/4I="}]}, {"Route": "css/webfonts/fa-solid-900.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394832"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Z6iAtLpSm5rMMD1ym1a0/XCG+yLUJWYEELjFHheE9ik=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z6iAtLpSm5rMMD1ym1a0/XCG+yLUJWYEELjFHheE9ik="}]}, {"Route": "css/webfonts/fa-solid-900.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "149908"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU+pg7kI/4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU+pg7kI/4I="}]}, {"Route": "css/webfonts/fa-solid-900.xyf340uas6.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-solid-900.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394832"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"Z6iAtLpSm5rMMD1ym1a0/XCG+yLUJWYEELjFHheE9ik=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xyf340uas6"}, {"Name": "label", "Value": "css/webfonts/fa-solid-900.ttf"}, {"Name": "integrity", "Value": "sha256-Z6iAtLpSm5rMMD1ym1a0/XCG+yLUJWYEELjFHheE9ik="}]}, {"Route": "css/webfonts/fa-v4compatibility.0bf02qslzm.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4536"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0bf02qslzm"}, {"Name": "label", "Value": "css/webfonts/fa-v4compatibility.woff2"}, {"Name": "integrity", "Value": "sha256-fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw="}]}, {"Route": "css/webfonts/fa-v4compatibility.1rckkh26l5.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10172"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1rckkh26l5"}, {"Name": "label", "Value": "css/webfonts/fa-v4compatibility.ttf"}, {"Name": "integrity", "Value": "sha256-jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8="}]}, {"Route": "css/webfonts/fa-v4compatibility.ttf", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10172"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8="}]}, {"Route": "css/webfonts/fa-v4compatibility.woff2", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\css\\webfonts\\fa-v4compatibility.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4536"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "img/angle_button_white.hoal44qztg.svg", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\angle_button_white.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "507"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hoal44qztg"}, {"Name": "label", "Value": "img/angle_button_white.svg"}, {"Name": "integrity", "Value": "sha256-r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg="}]}, {"Route": "img/angle_button_white.svg", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\angle_button_white.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "507"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg="}]}, {"Route": "img/sessionnotavailable.png", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\sessionnotavailable.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1072"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kIRR6icIjLjhvJTiRMZcxN9T74VR8+q5HvYJnmDI0iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kIRR6icIjLjhvJTiRMZcxN9T74VR8+q5HvYJnmDI0iw="}]}, {"Route": "img/sessionnotavailable.wwx3912tbx.png", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\sessionnotavailable.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1072"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kIRR6icIjLjhvJTiRMZcxN9T74VR8+q5HvYJnmDI0iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wwx3912tbx"}, {"Name": "label", "Value": "img/sessionnotavailable.png"}, {"Name": "integrity", "Value": "sha256-kIRR6icIjLjhvJTiRMZcxN9T74VR8+q5HvYJnmDI0iw="}]}, {"Route": "img/ticket-shape-bottom.kbtng4gjpy.svg", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\ticket-shape-bottom.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "566"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"MFTmQZ3+SjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbtng4gjpy"}, {"Name": "label", "Value": "img/ticket-shape-bottom.svg"}, {"Name": "integrity", "Value": "sha256-MFTmQZ3+SjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8="}]}, {"Route": "img/ticket-shape-bottom.svg", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\img\\ticket-shape-bottom.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "566"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"MFTmQZ3+SjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MFTmQZ3+SjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8="}]}, {"Route": "js/basket/basket.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\basket\\basket.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"adSEjCz5rrFNzZyRA/cUsa7Thv54opWordKjrxhuF94=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 12:46:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-adSEjCz5rrFNzZyRA/cUsa7Thv54opWordKjrxhuF94="}]}, {"Route": "js/basket/basket.tja0fkan1w.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\basket\\basket.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"adSEjCz5rrFNzZyRA/cUsa7Thv54opWordKjrxhuF94=\""}, {"Name": "Last-Modified", "Value": "Fri, 29 Aug 2025 12:46:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tja0fkan1w"}, {"Name": "label", "Value": "js/basket/basket.js"}, {"Name": "integrity", "Value": "sha256-adSEjCz5rrFNzZyRA/cUsa7Thv54opWordKjrxhuF94="}]}, {"Route": "js/bootstrap-spinner/bootstrap-input-spinner.91h9pnmz7i.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\bootstrap-spinner\\bootstrap-input-spinner.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "91h9pnmz7i"}, {"Name": "label", "Value": "js/bootstrap-spinner/bootstrap-input-spinner.js"}, {"Name": "integrity", "Value": "sha256-fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA="}]}, {"Route": "js/bootstrap-spinner/bootstrap-input-spinner.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\bootstrap-spinner\\bootstrap-input-spinner.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA="}]}, {"Route": "js/catalog/catalog.aybzvw0lo8.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\catalog\\catalog.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4481"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM+QIx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aybzvw0lo8"}, {"Name": "label", "Value": "js/catalog/catalog.js"}, {"Name": "integrity", "Value": "sha256-Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM+QIx4="}]}, {"Route": "js/catalog/catalog.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\catalog\\catalog.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4481"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM+QIx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM+QIx4="}]}, {"Route": "js/categprices/categprices.eg79ao16si.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\categprices\\categprices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/sdO8jhyJ/Nql0tNPT+5r6PTcOVTp6u2OHwrL1aWNaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eg79ao16si"}, {"Name": "label", "Value": "js/categprices/categprices.js"}, {"Name": "integrity", "Value": "sha256-/sdO8jhyJ/Nql0tNPT+5r6PTcOVTp6u2OHwrL1aWNaY="}]}, {"Route": "js/categprices/categprices.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\categprices\\categprices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/sdO8jhyJ/Nql0tNPT+5r6PTcOVTp6u2OHwrL1aWNaY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/sdO8jhyJ/Nql0tNPT+5r6PTcOVTp6u2OHwrL1aWNaY="}]}, {"Route": "js/commons.h0qjbqpakw.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\commons.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21537"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ+rtjV1GC0RM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h0qjbqpakw"}, {"Name": "label", "Value": "js/commons.js"}, {"Name": "integrity", "Value": "sha256-j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ+rtjV1GC0RM="}]}, {"Route": "js/commons.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\commons.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21537"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ+rtjV1GC0RM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ+rtjV1GC0RM="}]}, {"Route": "js/crossSelling/crossSelling.arhin6lv8f.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\crossSelling\\crossSelling.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "484"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mnZLlcvkdD2Q1FdNxspr+KAEC3ZJa1RUOp4K2+rJMLE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "arhin6lv8f"}, {"Name": "label", "Value": "js/crossSelling/crossSelling.js"}, {"Name": "integrity", "Value": "sha256-mnZLlcvkdD2Q1FdNxspr+KAEC3ZJa1RUOp4K2+rJMLE="}]}, {"Route": "js/crossSelling/crossSelling.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\crossSelling\\crossSelling.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "484"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mnZLlcvkdD2Q1FdNxspr+KAEC3ZJa1RUOp4K2+rJMLE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mnZLlcvkdD2Q1FdNxspr+KAEC3ZJa1RUOp4K2+rJMLE="}]}, {"Route": "js/feedbook/feedbookForm.9simdimwy5.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\feedbook\\feedbookForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16622"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9simdimwy5"}, {"Name": "label", "Value": "js/feedbook/feedbookForm.js"}, {"Name": "integrity", "Value": "sha256-mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts="}]}, {"Route": "js/feedbook/feedbookForm.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\feedbook\\feedbookForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16622"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts="}]}, {"Route": "js/homemodular/homemodular.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\homemodular\\homemodular.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5035"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU="}]}, {"Route": "js/homemodular/homemodular.pz7fifd5mo.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\homemodular\\homemodular.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5035"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pz7fifd5mo"}, {"Name": "label", "Value": "js/homemodular/homemodular.js"}, {"Name": "integrity", "Value": "sha256-NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU="}]}, {"Route": "js/insurance/insurance.5w9vrrajao.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\insurance\\insurance.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3952"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KxXX2S8dPxTQSYJi+ijA9oDxCm7CUxC2AhCHrhozgqs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5w9vrrajao"}, {"Name": "label", "Value": "js/insurance/insurance.js"}, {"Name": "integrity", "Value": "sha256-KxXX2S8dPxTQSYJi+ijA9oDxCm7CUxC2AhCHrhozgqs="}]}, {"Route": "js/insurance/insurance.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\insurance\\insurance.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3952"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KxXX2S8dPxTQSYJi+ijA9oDxCm7CUxC2AhCHrhozgqs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KxXX2S8dPxTQSYJi+ijA9oDxCm7CUxC2AhCHrhozgqs="}]}, {"Route": "js/lessModify.h35nv9j0tn.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\lessModify.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4607"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h35nv9j0tn"}, {"Name": "label", "Value": "js/lessModify.js"}, {"Name": "integrity", "Value": "sha256-jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY="}]}, {"Route": "js/lessModify.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\lessModify.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4607"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY="}]}, {"Route": "js/product/product.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\product.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8="}]}, {"Route": "js/product/product.l813fa0qah.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\product.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "315"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l813fa0qah"}, {"Name": "label", "Value": "js/product/product.js"}, {"Name": "integrity", "Value": "sha256-BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8="}]}, {"Route": "js/product/productDetails/productDetailsCADH.0x0wh8qap2.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\productDetails\\productDetailsCADH.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22862"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0x0wh8qap2"}, {"Name": "label", "Value": "js/product/productDetails/productDetailsCADH.js"}, {"Name": "integrity", "Value": "sha256-XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk="}]}, {"Route": "js/product/productDetails/productDetailsCADH.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\product\\productDetails\\productDetailsCADH.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22862"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk="}]}, {"Route": "js/seatsSelection/seatsSelection.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\seatsSelection\\seatsSelection.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g="}]}, {"Route": "js/seatsSelection/seatsSelection.y1y03gh8aq.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\seatsSelection\\seatsSelection.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y1y03gh8aq"}, {"Name": "label", "Value": "js/seatsSelection/seatsSelection.js"}, {"Name": "integrity", "Value": "sha256-Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g="}]}, {"Route": "js/session/pano.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\pano.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21094"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg="}]}, {"Route": "js/session/pano.lf2ncmqb44.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\pano.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21094"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lf2ncmqb44"}, {"Name": "label", "Value": "js/session/pano.js"}, {"Name": "integrity", "Value": "sha256-74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg="}]}, {"Route": "js/session/seatplan.8w64e9o242.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\seatplan.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "34600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz+z2hL8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8w64e9o242"}, {"Name": "label", "Value": "js/session/seatplan.js"}, {"Name": "integrity", "Value": "sha256-GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz+z2hL8="}]}, {"Route": "js/session/seatplan.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\seatplan.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "34600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz+z2hL8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 07:37:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz+z2hL8="}]}, {"Route": "js/session/session.3lezgcpngy.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\session.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110778"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abskm4cXOt2hXXgjXam+ebTyM1cQh/tA1TJZhwLpsd8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3lezgcpngy"}, {"Name": "label", "Value": "js/session/session.js"}, {"Name": "integrity", "Value": "sha256-abskm4cXOt2hXXgjXam+ebTyM1cQh/tA1TJZhwLpsd8="}]}, {"Route": "js/session/session.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\session\\session.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110778"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abskm4cXOt2hXXgjXam+ebTyM1cQh/tA1TJZhwLpsd8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abskm4cXOt2hXXgjXam+ebTyM1cQh/tA1TJZhwLpsd8="}]}, {"Route": "js/site.6b9nfc4n3r.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6b9nfc4n3r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]}, {"Route": "js/site.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]}, {"Route": "js/tunnel/tunnel.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\tunnel\\tunnel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MODq8+xYTX9x74jt+P1qSk4v7tqc6Y9f7c2v/+evnFg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MODq8+xYTX9x74jt+P1qSk4v7tqc6Y9f7c2v/+evnFg="}]}, {"Route": "js/tunnel/tunnel.sp16istu3z.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\tunnel\\tunnel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4691"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MODq8+xYTX9x74jt+P1qSk4v7tqc6Y9f7c2v/+evnFg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sp16istu3z"}, {"Name": "label", "Value": "js/tunnel/tunnel.js"}, {"Name": "integrity", "Value": "sha256-MODq8+xYTX9x74jt+P1qSk4v7tqc6Y9f7c2v/+evnFg="}]}, {"Route": "js/widget.5yj8jvbnmo.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32060"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"A6qF4CNZE8mLG516CVfmo8rso7giA09LEnGainFRT3c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:02:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5yj8jvbnmo"}, {"Name": "label", "Value": "js/widget.js"}, {"Name": "integrity", "Value": "sha256-A6qF4CNZE8mLG516CVfmo8rso7giA09LEnGainFRT3c="}]}, {"Route": "js/widget.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32060"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"A6qF4CNZE8mLG516CVfmo8rso7giA09LEnGainFRT3c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 13:02:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A6qF4CNZE8mLG516CVfmo8rso7giA09LEnGainFRT3c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.k9n1kkbua6.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "492048"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k9n1kkbua6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "492048"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.gawgt6fljy.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gawgt6fljy"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155764"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.kao5znno1s.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kao5znno1s"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "625953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.etnb7xlipe.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155764"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etnb7xlipe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.9n0ta5ieki.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0ta5ieki"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68266"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.0i1dcxd824.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151749"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i1dcxd824"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "151749"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48494"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.jzb7jyrjvs.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jzb7jyrjvs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "108539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.vxs71z90fw.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48494"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vxs71z90fw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5227"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.tgg2bl5mrw.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76483"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgg2bl5mrw"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.brwg1hntyu.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4028"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "brwg1hntyu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4028"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gf2dxac9qe.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gf2dxac9qe"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.nynt4yc5xr.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5227"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nynt4yc5xr"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.48vr37mrsy.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136072"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48vr37mrsy"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "229924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "402249"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.pxamm17y9e.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "402249"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pxamm17y9e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.hjuzisly30.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hju<PERSON><PERSON>ly30"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78641"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311949"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.u9xms436mi.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311949"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u9xms436mi"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.u0biprgly9.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "229924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u0biprgly9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "136072"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.vaswmcjbo4.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vaswmcjbo4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "190253"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.sgi57dik4g.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "190253"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sgi57dik4g"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zu238p5lxg.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zu238p5lxg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0="}]}, {"Route": "lib/bootstrap/LICENSE.weyt030wr8", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "weyt030wr8"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "287630"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="}]}, {"Route": "lib/jquery/dist/jquery.min.5trh6b1mit.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5trh6b1mit"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="}]}, {"Route": "lib/jquery/dist/jquery.min.dsw5v3fbc5.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137974"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dsw5v3fbc5"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89476"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137974"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="}]}, {"Route": "lib/jquery/dist/jquery.vwa26bmbsk.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "287630"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwa26bmbsk"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="}]}, {"Route": "lib/jquery/LICENSE.afgyafcsqt.txt", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "afgyafcsqt"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1641"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43184"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18467"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.sjab29p8z5.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18467"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjab29p8z5"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.wus95c49fh.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "43184"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wus95c49fh"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.jdpnbaa6vo.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48676"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jdpnbaa6vo"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48676"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yazfaIh2SXu8rPenyD2f36pKgrkv5XT+DQCDpZ/eDao="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.59qolqi738.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23261"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "59qolqi738"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23261"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F6h55Qw6sweK+t7SiOJX+2bpSAa3b/fnlrVCJvmEj1A="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.0td7jq9nxb.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0td7jq9nxb"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5867"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.v12ed9ioy0.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5867"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v12ed9ioy0"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.wqejeusuyq.txt", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]}, {"Route": "websiteexterneDemo/testingCallOffersLocal.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\testingCallOffersLocal.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2520"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G626HHRkuN+sz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G626HHRkuN+sz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU="}]}, {"Route": "websiteexterneDemo/testingCallOffersLocal.pn1mdxt63j.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\testingCallOffersLocal.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2520"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G626HHRkuN+sz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pn1mdxt63j"}, {"Name": "label", "Value": "websiteexterneDemo/testingCallOffersLocal.html"}, {"Name": "integrity", "Value": "sha256-G626HHRkuN+sz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU="}]}, {"Route": "websiteexterneDemo/WebSiteExterne.2exqpgysr8.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterne.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11048"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ikQY9P79wVARu+344J9kh1cizTuYfzoXtvT2pzCh3j4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2exqpgysr8"}, {"Name": "label", "Value": "websiteexterneDemo/WebSiteExterne.html"}, {"Name": "integrity", "Value": "sha256-ikQY9P79wVARu+344J9kh1cizTuYfzoXtvT2pzCh3j4="}]}, {"Route": "websiteexterneDemo/websiteexterne.3d1vw2wx03.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "457"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB+wYMg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3d1vw2wx03"}, {"Name": "label", "Value": "websiteexterneDemo/websiteexterne.js"}, {"Name": "integrity", "Value": "sha256-WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB+wYMg="}]}, {"Route": "websiteexterneDemo/websiteexterne.6vn0p36f17.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2794"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA+3rVoU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6vn0p36f17"}, {"Name": "label", "Value": "websiteexterneDemo/websiteexterne.css"}, {"Name": "integrity", "Value": "sha256-LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA+3rVoU="}]}, {"Route": "websiteexterneDemo/websiteexterne.css", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2794"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA+3rVoU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA+3rVoU="}]}, {"Route": "websiteexterneDemo/WebSiteExterne.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterne.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11048"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ikQY9P79wVARu+344J9kh1cizTuYfzoXtvT2pzCh3j4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ikQY9P79wVARu+344J9kh1cizTuYfzoXtvT2pzCh3j4="}]}, {"Route": "websiteexterneDemo/websiteexterne.js", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteexterne.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "457"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB+wYMg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB+wYMg="}]}, {"Route": "websiteexterneDemo/WebSiteExterneCallLocal.3o5my4v6zj.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterneCallLocal.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11630"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zdf2S9P7cq/t3T18wOoppYsNJA4GMCzSOvBa+KKwsLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3o5my4v6zj"}, {"Name": "label", "Value": "websiteexterneDemo/WebSiteExterneCallLocal.html"}, {"Name": "integrity", "Value": "sha256-zdf2S9P7cq/t3T18wOoppYsNJA4GMCzSOvBa+KKwsLw="}]}, {"Route": "websiteexterneDemo/WebSiteExterneCallLocal.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\WebSiteExterneCallLocal.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11630"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zdf2S9P7cq/t3T18wOoppYsNJA4GMCzSOvBa+KKwsLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:42:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zdf2S9P7cq/t3T18wOoppYsNJA4GMCzSOvBa+KKwsLw="}]}, {"Route": "websiteexterneDemo/websiteExterneTunnel.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteExterneTunnel.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12901"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J9<PERSON>Msklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc="}]}, {"Route": "websiteexterneDemo/websiteExterneTunnel.rbsndwsa29.html", "AssetFile": "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\websiteexterneDemo\\websiteExterneTunnel.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12901"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 12:36:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rbsndwsa29"}, {"Name": "label", "Value": "websiteexterneDemo/websiteExterneTunnel.html"}, {"Name": "integrity", "Value": "sha256-J9<PERSON>Msklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc="}]}]}