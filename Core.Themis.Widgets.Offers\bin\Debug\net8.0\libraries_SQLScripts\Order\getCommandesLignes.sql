﻿
/*
declare @pidentityId int = 0
declare @porderId int = 0
declare @peventId int = 0
*/

IF @pidentityId = 0
BEGIN
	SELECT commande_ligne_id, c.identite_id as CmdIdentiteId, cl.identite_id as CmdLigneIdentiteId,
		manifestation_id, seance_id, dossier_id, type_ligne FROM commande_ligne cl 
		INNER JOIN commande c on c.commande_id =cl.commande_id 
		WHERE c.commande_id=@porderId and (@peventId = 0 OR @peventId = cl.manifestation_id)
END
ELSE

BEGIN /* mes commandes lignes ou celles dont je suis le payeur : */
	SELECT commande_ligne_id, c.identite_id as CmdIdentiteId, cl.identite_id as CmdLigneIdentiteId,
		manifestation_id, seance_id, dossier_id, type_ligne FROM commande_ligne cl 
		INNER JOIN commande c on c.commande_id =cl.commande_id 
		WHERE c.commande_id=@porderId AND (cl.identite_id=@pidentityId OR c.identite_id=@pidentityId) and (@peventId = 0 OR @peventId = cl.manifestation_id)
END					  
					  