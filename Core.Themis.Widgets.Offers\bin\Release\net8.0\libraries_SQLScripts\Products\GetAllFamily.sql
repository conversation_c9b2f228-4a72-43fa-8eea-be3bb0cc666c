﻿--DECLARE @pLangCode VARCHAR(5) = 'fr'

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

SELECT DISTINCT 
pf.Produit_Famille_ID AS Id,
IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
FROM produit p
INNER JOIN Produit_Lien_Sous_Famille plsf ON plsf.Produit_id = p.produit_id
INNER JOIN Produit_Famille pf ON pf.Produit_Famille_ID = plsf.Produit_Famille_ID
LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = @langueId
WHERE p.internet = 1
AND pf.Masquer = 0
