
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT c.lieu_id, c.categ_id, 
	case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as categ_nom,
	c.categ_code, c.pref_affichage, c.categ_couleur_id 
FROM categorie c

INNER JOIN lieu l on c.lieu_id = l.lieu_id
INNER JOIN seance s on s.lieu_id = l.lieu_id


LEFT OUTER JOIN traduction_categorie tc on tc.categ_id = c.categ_id and tc.langue_id = @LgId
WHERE s.seance_id = @psessionId
ORDER BY c.pref_affichage

