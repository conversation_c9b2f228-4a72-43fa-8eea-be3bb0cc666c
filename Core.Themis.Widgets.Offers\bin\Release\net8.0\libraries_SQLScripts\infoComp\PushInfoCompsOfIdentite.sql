﻿
/*
	
declare @chaine varchar(max)
set @chaine = '29:0|40:0|41:0'

declare @identiteId int
set @identiteId = 479
	
declare @filiere varchar(max)
set @filiere = 'VENTE' -- VENTE ou REABO

*/

declare @chaine varchar(max)
set @chaine = '[CHAINE]' --'29:0|40:0|41:0'

declare @identiteId int
set @identiteId = [IDENTITEID] --473
	
declare @filiere varchar(max)
set @filiere = '[FILIERE]' -- VENTE ou REABO


DECLARE db_cursor CURSOR FOR 
	--'29:0|40:0|41:0'
	select Name from splitstring(@chaine,'|')

OPEN db_cursor  
FETCH NEXT FROM db_cursor INTO @chaine  

WHILE @@FETCH_STATUS = 0  
BEGIN  


/* Decomposition de la chaine pasée en paramètre */

declare @infoCompId int
declare @InfoCompActive int -- true ou false pour savoir si elle est cochée
declare @cntInfoCompOfIdentity int

-- If you need exactly string_split functionality (without id column):
select @infoCompId = Name   from splitstring(@chaine,':')
where rownum = 0
select @InfoCompActive = Name from splitstring(@chaine,':')
where rownum = 1

--select *  from splitstring(@chaine,':')

select @cntInfoCompOfIdentity = COUNT(*) from identite_infos_comp where info_comp_id = @infoCompId and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'


-- infocom existe
if(@cntInfoCompOfIdentity > 0)
BEGIN	
	print 'info comp exist ' + convert(varchar(50), @infoCompId)
	if(@InfoCompActive = 0)
	BEGIN
		print 'info comp pas active ' + convert(varchar(50), @infoCompId)
		--select supprimer, 'O' as supprimerNew, getdate(), * from identite_infos_comp  where info_comp_id = @infoCompId  and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'
		update identite_infos_comp set supprimer = 'O', DateModification=GETDATE() where info_comp_id = @infoCompId  and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'
	END

	if(@InfoCompActive = 1)
	BEGIN
		print 'info comp active ' + convert(varchar(50), @infoCompId)
		--select  supprimer, 'N' as supprimerNew, GETDATE(), * from identite_infos_comp  where info_comp_id = @infoCompId  and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'
		update identite_infos_comp set supprimer = 'N', DateModification=GETDATE() where info_comp_id = @infoCompId  and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'
	END
END


--aucune ligne 
if(@cntInfoCompOfIdentity = 0 )
BEGIN
	print 'info comp existe pas ' + convert(varchar(50), @infoCompId)

	
	if(@InfoCompActive = 1)
	BEGIN
		print 'info comp active ' + convert(varchar(50), @infoCompId)
		
		insert into identite_infos_comp
			select top 1 @identiteId, @infoCompId, '','', @filiere,'','N', getdate(), getdate() from identite_infos_comp 		
	END


	if(@InfoCompActive = 0)
	BEGIN
		print 'info comp pas active ' + convert(varchar(50), @infoCompId)
	END

END	


      FETCH NEXT FROM db_cursor INTO @chaine 
END 

CLOSE db_cursor  
DEALLOCATE db_cursor 


select * from identite_infos_comp where info_comp_id = @infoCompId and identite_id = @identiteId and valeur3 like '%'+@filiere+'%'
