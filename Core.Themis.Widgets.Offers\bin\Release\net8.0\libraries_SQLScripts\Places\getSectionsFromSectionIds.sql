
/* */
DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT distinct s.section_id as section_id,
CASE WHEN st.section_nom is null then s.section_nom else st.section_nom end as section_name
FROM section s
LEFT JOIN traduction_section st on st.section_id = s.section_id and st.langue_id = @LgId
WHERE s.section_id in ({sectionsids})
