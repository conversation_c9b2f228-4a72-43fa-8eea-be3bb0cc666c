﻿/*

  declare @pFieldCodeId int = 4165
*/


  declare @specific_field_code varchar(max)
   declare @global_field_code_id int
select @global_field_code_id= global_field_id, @specific_field_code=fieldSpecificCode  from translate_fieldsCodesList where id = @pFieldCodeId

  declare @field_code varchar(max)
select @field_code = fieldCode from translate_fieldsGlobalTranslation where id = @global_field_code_id



--champ parent
if @specific_field_code = @field_code 
  BEGIN
    --supprime les variables du parent
     select 'variable' as fieldvariable, *  from translate_fieldsVariables where fieldId = @global_field_code_id
     delete from translate_fieldsVariables where fieldId = @global_field_code_id
    
    select 'codelist' as codelist, * from translate_fieldsCodesList where global_field_id = @global_field_code_id

     --supprime les traductions du parent
    select  'global_translation' as global_translation,* from translate_fieldsGlobalTranslation where id = @global_field_code_id and fieldCode=@field_code
     delete from translate_fieldsGlobalTranslation where id =@global_field_code_id and fieldCode=@field_code
  END
else
  BEGIN
  
    select 'codelist' as codelist, * from translate_fieldsCodesList where id = @pFieldCodeId
    select 'variable' as fieldvariable, *  from translate_fieldsVariables where fieldId = @pFieldCodeId

    delete  from translate_fieldsCodesList where id = @pFieldCodeId

    delete from translate_fieldsVariables where fieldId = @pFieldCodeId

  END
