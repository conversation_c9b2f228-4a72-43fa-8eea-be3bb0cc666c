﻿@using Core.Themis.Libraries.Razor.Areas.Catalog.ViewModels;
@using Core.Themis.Libraries.Razor.Areas.Catalog;

@model ProductAppSettings

@{
    ViewBag.Title = "Product";
    string UrlToPath = $"{@Context.Request.Scheme}://{@Context.Request.Host}{@Context.Request.PathBase}";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
}

@section styles {
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/catalog/style.less">
}

<component type="typeof(ProductApp)" render-mode="Server" param-AppSettings="@Model" />


@*************** SCRIPTS *************@
@section scripts {

    <script>
        var htmlSelector = "@Model.HtmlSelector";
    </script>
    <script src="@(UrlToPath)js/product/product.js"></script>
}