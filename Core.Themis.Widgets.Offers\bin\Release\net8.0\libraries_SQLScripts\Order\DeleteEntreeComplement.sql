﻿/*
declare @pentree_id int	  = 0
declare @pseance_id int	  = 0
declare @pconsumer_id int = 0
declare @pdossier_id int  = 0
declare @pidentite_id int = 0
*/


DECLARE @NbResult int



SELECT @NbResult = count(*) from Entree_Complement where entree_id = @pentree_id and Identite_id = @pidentite_id  and seance_id = @pseance_id

IF @NbResult = 1
BEGIN

	DELETE Entree_Complement where entree_id = @pentree_id and Identite_id = @pidentite_id  and seance_id = @pseance_id

END