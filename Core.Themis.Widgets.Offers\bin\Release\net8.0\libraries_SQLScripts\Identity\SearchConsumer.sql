/*
declare @pIdentityIdConnected int
declare @pIdentityToSearch int
declare @pEmail varchar(300)
declare @pInitial varchar(10)
declare @pAdherentId varchar(max) 
set @pAdherentId = '10991161270'

declare @postalTelEmail int
set @postalTelEmail= 6


set @pIdentityIdConnected =  749
set @pIdentityToSearch =  473
set @pInitial = 'mm'
--set @pEmail = '<EMAIL>'
*/



Declare @TblTemp table (Email varchar(250),Identite_id int)

if @pAdherentId != '' AND @pAdherentId != '0'
	BEGIN
		declare @reversidentite_id int
		set @reversidentite_id = dbo.F_adhesion_num_reverse(@pAdherentId)
		if (@reversidentite_id is not null)
		BEGIN 
			--set @myidentite_id = @reversidentite_id
			insert into @TblTemp
			select email, identite_id from (
				select 
				case when @postalTelEmail =1 then postal_tel1 else '' end +
				case when @postalTelEmail =2 then postal_tel2 else '' end +
				case when @postalTelEmail =3 then postal_tel3 else '' end +
				case when @postalTelEmail =4 then postal_tel4 else '' end +
				case when @postalTelEmail =5 then postal_tel5 else '' end +
				case when @postalTelEmail =6 then postal_tel6 else '' end +
				case when @postalTelEmail =7 then postal_tel7 else '' end
				as email, *
				from identite
				) s
				where identite_id = @reversidentite_id
				and identite_password <> '' and FicheSupprimer =  'N'
				order by identite_id desc

		END
	END
ELSE
	begin
		/*************** Combo ID et EMAIL *****************************/
		if @pIdentityToSearch > 0 and @pEmail != '' 
		begin

			
			insert into @TblTemp
			select email, identite_id from (
				select 
				case when @postalTelEmail =1 then postal_tel1 else '' end +
				case when @postalTelEmail =2 then postal_tel2 else '' end +
				case when @postalTelEmail =3 then postal_tel3 else '' end +
				case when @postalTelEmail =4 then postal_tel4 else '' end +
				case when @postalTelEmail =5 then postal_tel5 else '' end +
				case when @postalTelEmail =6 then postal_tel6 else '' end +
				case when @postalTelEmail =7 then postal_tel7 else '' end
				as email, *
				from identite
				) s
				where email =@pEmail and identite_id = @pIdentityToSearch
				and identite_password <> '' and FicheSupprimer =  'N'
				order by identite_id desc
		
		end

		/*************** Combo INITIAL et EMAIL *****************************/
		if @pInitial <> ''  and @pEmail != '' 
		begin
			
			insert into @TblTemp
			select email, identite_id from (
				select 
				case when @postalTelEmail =1 then postal_tel1 else '' end +
				case when @postalTelEmail =2 then postal_tel2 else '' end +
				case when @postalTelEmail =3 then postal_tel3 else '' end +
				case when @postalTelEmail =4 then postal_tel4 else '' end +
				case when @postalTelEmail =5 then postal_tel5 else '' end +
				case when @postalTelEmail =6 then postal_tel6 else '' end +
				case when @postalTelEmail =7 then postal_tel7 else '' end
				as email, *
				from identite
				) s
				where email =@pEmail
				and upper(SUBSTRING(ltrim(rtrim(identite_prenom)),1,1) + SUBSTRING(ltrim(rtrim(identite_nom)),1,1)) =upper(@pInitial)
				
				and identite_password <> ''
				and FicheSupprimer =  'N'
				order by identite_id desc
		
		end

		/*************** Combo INITIAL et ID *****************************/
		if @pInitial <> ''  and @pIdentityToSearch > 0 
		begin
			
			insert into @TblTemp
			select email, identite_id from (
				select 
				case when @postalTelEmail =1 then postal_tel1 else '' end +
				case when @postalTelEmail =2 then postal_tel2 else '' end +
				case when @postalTelEmail =3 then postal_tel3 else '' end +
				case when @postalTelEmail =4 then postal_tel4 else '' end +
				case when @postalTelEmail =5 then postal_tel5 else '' end +
				case when @postalTelEmail =6 then postal_tel6 else '' end +
				case when @postalTelEmail =7 then postal_tel7 else '' end
				as email, *
				from identite
				) s
				where identite_id  = @pIdentityToSearch
				and upper(SUBSTRING(ltrim(rtrim(identite_prenom)),1,1) + SUBSTRING(ltrim(rtrim(identite_nom)),1,1)) =upper(@pInitial)
				and identite_password <> ''
				and FicheSupprimer =  'N'
				order by identite_id desc

		end

	end



declare @nbIdentity int
select @nbIdentity = count(*) from @TblTemp


if @nbIdentity = 1
BEGIN
	--select * from @TblTemp

	declare @identityId int
	set @identityId = (select identite_id from @TblTemp)

	/*scinder dans un autre sql (linkconsumer.sql)
	INSERT INTO groupe_consomateur(donneur_ordre_identite_id,consomateur_identite_id)
				select @pIdentityIdConnected, @identityId 
				where 0=(select  COUNT(*)  from groupe_consomateur where donneur_ordre_identite_id=@pIdentityIdConnected and consomateur_identite_id=@identityId )
		*/	
	select @identityId	
END

	--select @nbIdentity


if @nbIdentity > 1
begin 
	select -2
end

if @nbIdentity = 0
begin
	select -1 
end


