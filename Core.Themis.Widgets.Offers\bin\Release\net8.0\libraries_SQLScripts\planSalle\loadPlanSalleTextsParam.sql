﻿/* \\**************\webservices\dev\libraries\LIBRARIES_SQLSCRIPTS\planSalle\loadPlanSalleTextsParam.sql */ 
 	
DECLARE @myZones table (zone_id int)
INSERT INTO @myZones 
SELECT zone_id FROM zone union
SELECT 0

--select etage_id into #myFloors FROM etage

DECLARE @myFloors table (etage_id int)
INSERT INTO @myFloors 
select etage_id FROM etage union
select 0

--select section_id into #mySections FROM section

DECLARE @mySections table (section_id int)
INSERT INTO @mySections 
select section_id FROM section union
SELECT 0
 
if (@pFirstzoneid >0)
begin
	DELETE @myZones WHERE zone_id not in ({listzonesId})
end
if (@pFirstfoorId >0)
begin	
	DELETE @myFloors WHERE etage_id not in ({listfloorsId})
end
if (@pFirstsectionId >0)
begin	
	DELETE @mySections WHERE section_id not in ({listsectionsId})
end
	 
DECLARE @typePlan int	 
SELECT @typePlan = lieu_physique_type_plan from seance s
INNER JOIN lieu_configuration lc on lc.lieu_config_id = s.lieu_config_id
INNER JOIN lieu_physique lp on lp.lieu_physique_id = lc.lieu_physique_id
WHERE seance_id = @pSessionId

SELECT type_siege, 
case when @typePlan=2 and type_siege='L£' then convert(int, pos_x * 19.0/18.0) else pos_x end as pos_x,
case when @typePlan=2 and type_siege='L£' then convert(int,pos_y * 19.0/18.0) else pos_y end as pos_y,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_x * 19.0/18.0) else decal_x end as decal_x,
case when @typePlan=2 and type_siege='L£' then convert(int,decal_y * 19.0/18.0) else decal_y end as decal_y,
texte,
case when len(type_siege)=2 then
		case when type_siege = 'L¤' 
			then 'isPoteau' 
			else
				case when type_siege='L£' then 'isTrait' + convert(varchar(1), @typePlan) else 'isTexte'
			end
		end 
	else
		case when SUBSTRING(type_siege,1,1)='£' then 'TexteLong' else '??' end
	end	
	as typeTEXTE,
	case when len(type_siege)=2 then
		case when type_siege = 'L¤' 
		then '°°' 
		when type_siege='L£' then
			texte
		else
			SUBSTRING(type_siege,2,1)
		end 
	else
		case when SUBSTRING(type_siege,1,1)='£' then 
			texte + '|' + type_siege else '' end 
	end		
as valTEXTE
,@typePlan as type_plan

FROM reference_lieu_physique rlp
	inner join	 seance s on s.lieu_config_id=rlp.lieu_physique_id
	inner join @myZones z on rlp.zone_id = z.zone_id
	inner join @myFloors et on rlp.etage_id = et.etage_id
	inner join @mySections sect on rlp.section_id = sect.section_id
WHERE seance_id= @pSessionId
AND type_siege<>'F' AND type_siege NOT LIKE 'S%' 
--and type_siege<>'L£'

ORDER BY iindex