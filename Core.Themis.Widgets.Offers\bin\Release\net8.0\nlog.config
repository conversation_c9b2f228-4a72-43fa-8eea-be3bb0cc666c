﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
	</extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- File Target for all log messages with basic details -->

		<target xsi:type="File" name="alltracesbystructure"
				archiveAboveSize ="1000000"
				archiveFileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\archive\str.${event-properties:item=structureid:whenEmpty=0}.${var:version:whenEmpty=0}.nlog-ALL.{#}.log"
				archiveNumbering="Rolling"
				maxArchiveFiles="10"
				fileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\str.${event-properties:item=structureid:whenEmpty=0}.${var:version:whenEmpty=0}.nlog-ALL.log"
				layout="${longdate}|${machinename} - IP:${aspnet-request-ip}|ALL|${version:whenEmpty=0}|${var:version:whenEmpty=0}|${event-properties:item=structureid:whenEmpty=0}|${level:uppercase=true}|${message} ${exception:format=tostring}" />

		<target xsi:type="File" name="informationsbystructure"
				archiveAboveSize ="1000000"
				archiveFileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\archive\str.${event-properties:item=structureid:whenEmpty=0}.${var:version:whenEmpty=0}.nlog-INFOS.{#}.log"
				archiveNumbering="Rolling"
				maxArchiveFiles="10"
		
		
				fileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\str.${event-properties:item=structureid:whenEmpty=0}.${var:version:whenEmpty=0}.nlog-INFOS.log"
				layout="${longdate}|INFO|${level:uppercase=true}|${message} ${exception:format=tostring}" />


		<target xsi:type="File" name="informationsallstructures"
				archiveAboveSize ="1000000"
				archiveFileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\archive\str.ALL.${var:version:whenEmpty=0}.nlog-INFOS.{#}.log"
				archiveNumbering="Rolling"
				maxArchiveFiles="10"
				fileName="c:\LOGS\Sites\WIDGETS\OFFERS\${aspnet-environment}\str.ALL.${var:version:whenEmpty=0}.nlog-INFOS.log"
				layout="${longdate}|INFO|${level:uppercase=true:padding=-5}|${event-properties:item=structureid:whenEmpty=0:padding=-4}|${message} ${exception:format=tostring}" />

	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<!--All logs, including from Microsoft-->

		<logger name="*" minlevel="Trace" writeTo="alltracesbystructure" />
		<logger name="*" minlevel="Information" writeTo="informationsbystructure" />
		<logger name="*" minlevel="Information" writeTo="informationsallstructures" />


		<!--Output hosting lifetime messages to console target for faster startup detection -->
		<logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="lifetimeConsole, ownFile-web" final="true" />

		<!--Skip non-critical Microsoft logs and so log only own logs (BlackHole) -->
		<logger name="Microsoft.*" maxlevel="Info" final="true" />
		<logger name="System.Net.Http.*" maxlevel="Info" final="true" />


	</rules>
</nlog>