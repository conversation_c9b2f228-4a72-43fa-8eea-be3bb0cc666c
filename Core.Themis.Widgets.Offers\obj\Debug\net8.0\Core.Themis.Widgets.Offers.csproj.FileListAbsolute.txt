D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\GetPropertieOfManifsByManifIdAndRefCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Razor.staticwebassets.runtime.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Razor.staticwebassets.endpoints.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\web.config
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\appsettings.Development.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\appsettings.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\appsettings.Local.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\appsettings.Staging.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.staticwebassets.runtime.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.staticwebassets.endpoints.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.exe
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportLieusSeancesFutures.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleAvailabilitiesParam.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Repositories\Open\ParamsDeflag\Interfaces\IParamsDeflagRepository.cs
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetAllFormulasWithTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetCatalogFormuleAboByFilter.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Abonnement\GetFormuleAbonnement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.146.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\checkAdhesionToConsumerProduct.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdherentIdByIdentityId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionCatalogsAndProperties.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionCatalogsOfIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionFromGpId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getAdhesionOfIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\getDetailAdhesionOfIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Adhesions\insertAdhesionDossierEntree.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\BarCode\GetBarCodeByBarCodeAndColEmail.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\BarCode\GetBarCodeManif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\BarCode\SetBarCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetAllFamilliesProducts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetBoutiqueProductById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetFamilleById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Boutique\GetSousFamilleById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Boutique\LoadFamiliesSubFamiliesProducts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\BuyerProfil\getBuyerProfilById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\BuyerProfil\getBuyerProfilByLoginPassw.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Categories\GetCategoriesForSessions.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Cinema\GetTranslationOfDisciplineByIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Cinema\GetTranslationOfLocalisationByIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Cinema\LoadDisciplineByEventId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Cinema\LoadLocalisationsBySessionId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Consumers\createConsumer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\CoupeFile\loadOrdersOfInternetUser.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\CrossSelling\getCrossSelling.146.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\CrossSelling\getCrossSelling.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_Get.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_getMontantReprise.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_IsAutorise.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\DepotVente\depotVente_push.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Distanciation\getDistanciation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Distanciation\updateSeatsDistanciation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupsByEventTypeId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEvents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetAllEventsGroupWithEventsForSelect.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventGroupTranslationById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndGenresId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsByEventTypesIdAndTargetsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsGroupsOnSaleByEventsTypes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\EventsGroups\GetEventsTypeOnSale.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetAllGenresByEventTypeId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreByEventSubGenreId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetEventsGenreOnSaleByEventsTypes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndEventGroupsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\Genres\GetGenresByEventTypesIdAndTargetsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetAllEventsAfterADate.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetCatalogEventsByFilter.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventGenreById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventGroupById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventSessionOfSession.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsGeneralsAndAdhesionsOfferByFilter.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsGroupsOfEventsSales.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsInAnInterval.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getEventsInfos.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsOnSale.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetEventsOnSaleIntoAdhesionOffer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getIndivScheduleByIdentitePA.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosByEventId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosEventForHomeModular.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetInfosOfEventByIdAndLangCodeWithDependencies.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetManifestationImageByEventId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSessionsEntitiesInAnInterval.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSessionsInfos.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\getSubGenreById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationEventInfos.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfEventByIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfLockByEventIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfLockManifByEventId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListByEventIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\GetTranslationOfWaitingListById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPrice.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPriceAbo.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\InsertLowestPriceManif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsFeatures.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsGroups.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadEventsOfThisFormulaWithTranslation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadGenreSousGenre.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadPricesGridOfEvents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadSessionsPricesOfEvents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\loadSessionsZoneFloorSectionCategPricesOfEvents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\DeletePropertieOfManif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\InsertPropertieOfManif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesOfManifs\UpdatePropertieOfManif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\ProprertiesReferences\GetPropertieReferenceOfManifsByRefCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSousGenresOnSaleByGenre.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventsTypesId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndEventGroupsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndGenresId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetSubGenresByEventTypesIdAndTargetsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\EventsSessions\SousGenres\GetTranslationOfSousGenreAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportCustomersChanges.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportCustomersInfoCompChanges.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportDetailBilletsByEvent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEtatBilletterie.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEtatBilletterieEmis.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportEventsGauges.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsGaugev0.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsPriceGridBCubix.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportEventsPriceGridReelax.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportGlobalMoves.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportMouvementsProducts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportMouvementsSeats.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportRestantBySeanceCategReserve.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportsRecetteByEventSession.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\exportsVardar.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportsVardarAccessControl.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\ExportVardarAllEventsSessionsPlaces.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGgetDatas.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGupdateDatas.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\SoftwareAGupdateDatasV2.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Exports\TestGetManifByGroupId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\---getGrilleTarifs_BigOne.218.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\dispoBySeanceCategForOperateur.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllByEventIdAndSessionIdAndOffersId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetDispoFromGestionPlacesWithOffer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceTypeEnvoiByEventSeddionCategTypeTarif.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetGestionPlaceWithOfferBySessionIdAndCategoryId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGpsByIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.146.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.553.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.614.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.744.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getGrilleTarifs_BigOne.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivPricesCategs.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivSessions.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getIndivSessionsByIdentitePA.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetInfosOfSessionsByIdentitePA.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getLowerPriceOfEvent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByGpsIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.412.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.655.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\getMObentionByProductsIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetReserveIdsFromGestionPlacesWithOffer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\loadGestionPlaceTypeEnvoi.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\loadTarifCommentaires.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAddSupp.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.150.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.218.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.347_saison2324.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.661.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\PanierTransformatorAdhesion.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\èèPanierTransformatorAdhesion.146.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GpManifestation\CreateColumnsForWaitingListAndTraduction.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GpSeance\CreateColumnTraductionForLock.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\checkHomeModularBlockUserConfig.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\deleteHomeModularBlockEmplacement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetAllEmplacementGroups.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetBlockEmplacementByBlockTypeId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\GetHomeModularBlockEmplacementById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockEmplacementList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularBlockUserConfigList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupByIdWithDependencies.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\getHomeModularEmplacementGroupList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockEmplacement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\insertHomeModularBlockUserConfig.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures - Copier.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\LoadProductsFeatures.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\Clients\UpdateBlockUserConfigValuesTemp.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getBlockFunctionByIdWithDependencies.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\GetBlockFunctionsByBlockTypeIdWithDependencies.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\GetFunctionsOfBlockTypeById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getHomeModularBlockTypeList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\HomeModular\getHomeModularFunctionsList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddConsumer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentiteComplement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentiteComplementWeb.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentity - Copie.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\AddInfoCompOnIdentite.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetConsommateurs.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\getIdentiteForPatHome.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdentity_Unidy.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetIdIdentiteComplementWebPassword.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\GetLibelleTels.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\LinkConsumer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\SearchConsumer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\SelectCiblesFromListIdentite.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\UpdateIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Identity\UpdatePasswordIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\DeleteInfoCompFromEmails.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\DeleteInfoCompFromIdentiteIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoComp.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoCompForEmails.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\PushInfoCompsOfIdentite.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Insurance\ActivateInsurance.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Insurance\createGlobalInsuranceOrder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Insurance\HasInsuranceProduct.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Languages\getLanguage.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\LogsPartenaires\getLogPartnerById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetDefaultMaquette.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetFontName.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\getMaquetteCoupeFile.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\loadMaquette.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Offers\GetOffresProfilAcheteur.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpenPartner\CheckPartnerIsValidRevender.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\--selectOrderIdOpen.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverage.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Average\selectOpinionOrderAverageGraph.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\isOpinionConfigured.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\isOpinionManaged.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Order\getOrdersFromIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Questions\getOpinionOrderList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\createBlankEvaluationsForm.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\createOpinionOrderResponses.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getBlanksEvaluationsFormsList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getNonUpdatedEvaluationsFormsList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\getOpinionOrderList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setDemandeAvisEnvoye.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setInfos.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateEvaluationsForm_setOrderId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\OpinionOrder\Responses\updateOpinionOrderForms.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelDossier.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelEntrees.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\cancelOrder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelOrderMiseEnAcompte.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\cancelOrderOld.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\CancelOrderReservation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\createCommandeLigne.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossier.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossierHisto.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\createSeatDossierSvg.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\DeleteEntreeComplement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\downgradeorder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllEntreesEditeesOfOrderById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllEntreesOfIdentiteById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllOrdersByIdentiteId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllOrderWithoutReservationByIdentiteId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationByIdentiteId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationOrderAvailableForPassCultureCollectiveOffer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetAllReservationUpdatablePassCultureCollectiveOffer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getCommandesLignes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getDossiersProductFromOrderId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getDossiersSeatsFromOrderId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetEntreeByCriteria.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getEventSessionPrice.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderByIdForInsurance.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderDetailFromId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderFromId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrderFromId_Buyer.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\GetOrderIdsByBarcodes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getOrdersFromIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.224.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.281.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\getReservationsOfIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\InsertEntreeComplement.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\updatecommandeLigne_icone.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Order\updateDossier_dossierC.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\createPartner.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\createPartnerStructureLink.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\deletePartnerStructureLink.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartner.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\GetPartnerById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\GetPartnerByName.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnerId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnerRoles.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\getPartnersList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\getStructuresOfPartner.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartner.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartnerRoleLink.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Partner\updatePartnerStructuresLink.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Places\getFloorsFromFloorIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Places\getPlacesFromSessionsIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Places\getSectionsFromSectionIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Places\getZonesFromZoneIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllAlotissements.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllCategs.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllCategsBySession.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllContingents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllDenominations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllFloors.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllReserves.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllSections.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllTribunesGatesAcces.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadAllZones.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalle.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleParam.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleParamIndispo.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleTexts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\planSalle\loadPlanSalleTextsParam.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllFamily.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductFamily.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductFamilyKeyValues.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProducts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductsInternet.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductsWithTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllSubFamilyByProductFamilyId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAssuranceProduct.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetFraisDossierByFiliere.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\getMaquettesProductsByMo.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\getProductForPatHome.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\getProductForPatHomeCore.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsEvent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsGlobaux.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\LoadFamiliesSubFamiliesProductsSession.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettesFromBarCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Recettes\getRecettesProducts.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Recettes\insertLigneRecetteBillet.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Recettes\setBarCodeRecette.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\createReservationOrder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\createReservationScheduleReminder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getInfoResaForBasket.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getReservationsOfIdentity.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\getReservationsReminder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\isResa_alive.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\reservationEvents.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\updateReservationOrder.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ReservationOrder\updateScheduleReservationOrder_setEnvoye.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Reserves\reservesOfOperateur.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\SaleChannel\getSaleChannelList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\SaleChannel\selectSaleChannelOpen.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\SaleRulesAndCo\getBuyerProfil.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\FlagAuto.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\flagSeat.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\GetAllEntreesEditeesBySeanceIdAndIdentiteId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeatForPatHome.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeats.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\getSeats_lang.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\unflagSeats.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreeControleAccess.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreeReservation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Seats\UpdateEntreesPayees.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsByEventId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsByEventIdOnSale.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Session\GetAllSessionsTicketsByIdentiteId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Session\GetSessionById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetCardsToSend.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsoredAmount.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsors.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\GetSponsorUsedRemaining.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\SetDateSent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Sponsors\SetDateSentNull.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Structures\open_getStructureInfos.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetAllTargetsByEventTypeId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndEventGroupsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTargetsByEventTypesIdAndGenresId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\GetTranslationOfTargetByIdAndLangCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\LoadTargetsBySessionId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Targets\LoadTargetsOnSaleByEventsTypes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ThemisSupportTools\InsertTstAccess.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\--getSpecificTranslationsParent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetSpecificClientTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\getSpecificTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\getSpecificTranslationsParent.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetTranslationByKeyword.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\GetTranslationsLikeKeyword.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Clients\insertSpecificTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\--updateTranslationsFieldVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteDefaultTranslation.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\DeleteTranslateFieldsCodesList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsArea.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsFieldVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\deleteTranslationsVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetGlobalFieldsTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetGlobalTranslationById.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getGlobalTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslateFieldsCodesList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslationByKeyword.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\GetTranslationLikeKeyword.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsAreasList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsFieldCodeList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsFields.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\gettranslationsFieldsCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsGlobalFields.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsTermsAndValues.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\getTranslationsVariablesList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateArea.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateFieldsCodesList.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\InsertTranslateVariables.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsArea.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsFieldVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\insertTranslationsVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslations.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsArea.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsFieldCode.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\updateTranslationsVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ValeurTarifStock\getValeurTarifStock.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddPaiementAction.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddProductResa.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\AddSeatsIndiv.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.150.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.218.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckGpIdEnCours.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\CheckProductAdhesionEnCours.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllProductCarteAdhesion.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllProductEventSessionGlobal.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteAllSessionsPanierEntree.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeleteMObentionOnBasketId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit.412.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\DeletePanierProduit_prodId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\deleteSeatInBasketLine.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBaskets.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBasketsFromOrdersId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getBasketsWebUsers.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetCartesAdhesionEnCours.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetGpIdEnCours.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getPaiementActions.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\getPaiementActions_baskets.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetPanierFromWebUser.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\InsertPanierProduit.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\InsertPanierProduitCarteAdhesionProps.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateConsumerEntrees.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateEtatPanier.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateEtatPanierStateToState.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\UpdateTypeEnvoiAndMaquette_products.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\SaleDatas\getSaleDatas.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\fillInfosWTDataBase.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationActions.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getLogsEtapesCreationCmd.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmd.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_createcmdBasketId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflag.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToDo_reflagBasketId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToInform.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPaniersToSendEmailError.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\getPanierToDo_straight.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeCommande.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\insertLogEtapeInformEmail.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\RestartStepPanier.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\setEmailErrorEnvoye.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\ServicesAsynch\updatePanierToDo_straight.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\CheckSponsorPanierEntreesExist.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetAllSponsorPanierEntrees.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfMyBasket.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorReferenceOfOtherBasket.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\GetSponsorsPanierEntree.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Sponsors\InsertSponsorPanierEntree.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetLogsWebUserId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUser.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUsers.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\GetWebUsersCount.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\InsertWebUser.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\WebUsers\UpdateCouponPromo.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\nlog.config
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.deps.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.runtimeconfig.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Widgets.Offers.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Antiforgery.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.BearerToken.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.Cookies.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authentication.OAuth.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authorization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Authorization.Policy.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.Authorization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.Endpoints.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.Forms.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.Server.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Components.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Connections.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.CookiePolicy.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Cors.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Cryptography.Internal.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.DataProtection.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.DataProtection.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.DataProtection.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Diagnostics.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Diagnostics.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.HostFiltering.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Hosting.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Hosting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Html.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Connections.Common.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Connections.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Features.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Http.Results.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.HttpLogging.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.HttpOverrides.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.HttpsPolicy.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Identity.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Localization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Localization.Routing.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Metadata.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Cors.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Formatters.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Localization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.Razor.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.RazorPages.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.TagHelpers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Mvc.ViewFeatures.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.OutputCaching.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.RateLimiting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Razor.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Razor.Runtime.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.RequestDecompression.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.ResponseCaching.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.ResponseCompression.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Rewrite.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Routing.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Routing.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.HttpSys.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.IIS.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.IISIntegration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.Kestrel.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.Kestrel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.Session.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.SignalR.Common.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.SignalR.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.SignalR.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.SignalR.Protocols.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.StaticFiles.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.WebSockets.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.AspNetCore.WebUtilities.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.CSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.Binder.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.CommandLine.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.Ini.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.KeyPerFile.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.UserSecrets.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Configuration.Xml.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Diagnostics.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Diagnostics.HealthChecks.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Features.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.FileProviders.Composite.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.FileProviders.Embedded.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.FileProviders.Physical.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.FileSystemGlobbing.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Hosting.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Hosting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Http.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Identity.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Identity.Stores.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Localization.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Localization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.Configuration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.Console.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.Debug.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.EventLog.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.EventSource.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Logging.TraceSource.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.ObjectPool.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Options.ConfigurationExtensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.Options.DataAnnotations.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Extensions.WebEncoders.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.JSInterop.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Net.Http.Headers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.VisualBasic.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.VisualBasic.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Win32.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\Microsoft.Win32.Registry.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\mscorlib.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\netstandard.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.AppContext.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Buffers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Collections.Concurrent.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Collections.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Collections.Immutable.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Collections.NonGeneric.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Collections.Specialized.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.Annotations.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.DataAnnotations.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.EventBasedAsync.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ComponentModel.TypeConverter.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Configuration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Console.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Data.Common.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Data.DataSetExtensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Data.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.Contracts.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.Debug.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.EventLog.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.FileVersionInfo.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.Process.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.StackTrace.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.TextWriterTraceListener.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.Tools.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.TraceSource.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Diagnostics.Tracing.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Drawing.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Drawing.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Dynamic.Runtime.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Formats.Tar.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Globalization.Calendars.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Globalization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Globalization.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Compression.Brotli.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Compression.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Compression.FileSystem.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Compression.ZipFile.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.FileSystem.AccessControl.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.FileSystem.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.FileSystem.DriveInfo.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.FileSystem.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.FileSystem.Watcher.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.IsolatedStorage.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.MemoryMappedFiles.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Pipes.AccessControl.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.Pipes.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.IO.UnmanagedMemoryStream.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Linq.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Linq.Expressions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Linq.Parallel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Linq.Queryable.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Memory.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Http.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Http.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.HttpListener.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Mail.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.NameResolution.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.NetworkInformation.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Ping.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Quic.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Requests.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Security.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.ServicePoint.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.Sockets.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.WebClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.WebHeaderCollection.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.WebProxy.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.WebSockets.Client.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Net.WebSockets.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Numerics.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Numerics.Vectors.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ObjectModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.DispatchProxy.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Emit.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Emit.ILGeneration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Emit.Lightweight.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Metadata.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Reflection.TypeExtensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Resources.Reader.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Resources.ResourceManager.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Resources.Writer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.CompilerServices.Unsafe.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.CompilerServices.VisualC.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Handles.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.InteropServices.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.InteropServices.JavaScript.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.InteropServices.RuntimeInformation.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Intrinsics.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Loader.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Numerics.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Serialization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Serialization.Formatters.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Serialization.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Serialization.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Runtime.Serialization.Xml.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.AccessControl.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Claims.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Algorithms.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Cng.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Csp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Encoding.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.OpenSsl.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.X509Certificates.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Cryptography.Xml.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Principal.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.Principal.Windows.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Security.SecureString.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ServiceModel.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ServiceProcess.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Text.Encoding.CodePages.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Text.Encoding.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Text.Encoding.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Text.RegularExpressions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Channels.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Overlapped.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.RateLimiting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Tasks.Dataflow.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Tasks.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Tasks.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Tasks.Parallel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Thread.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.ThreadPool.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Threading.Timer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Transactions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Transactions.Local.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.ValueTuple.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Web.HttpUtility.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Windows.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.Linq.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.ReaderWriter.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.Serialization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.XDocument.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.XmlDocument.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.XmlSerializer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.XPath.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\System.Xml.XPath.XDocument.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\refs\WindowsBase.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\AutoMapper.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Azure.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Azure.Identity.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\BarcodeStandard.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\BlazorBootstrap.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\BlazorDateRangePicker.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\CoreHtmlToImage.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\CSharpMinifier.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Dapper.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Passbook.Generator.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Fluid.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Google.Apis.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Google.Apis.Auth.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Google.Apis.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Google.Apis.IAMCredentials.v1.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Humanizer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ISO3166.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\jose-jwt.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\JWT.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\log4net.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\MailKit.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Authorization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Components.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Authorization.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Forms.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Components.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Metadata.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Mvc.Razor.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.AspNetCore.Razor.Language.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Build.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Build.Framework.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.AnalyzerUtilities.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Features.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.Elfie.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.Features.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.Razor.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.Scripting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.DiaSymReader.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.DotNet.Scaffolding.Shared.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Caching.StackExchangeRedis.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Configuration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Identity.Client.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.JSInterop.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.NET.StringTools.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.OpenApi.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGeneration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGeneration.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\dotnet-aspnet-codegenerator-design.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\MimeKit.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Mono.Cecil.Mdb.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Mono.Cecil.Pdb.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Mono.Cecil.Rocks.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Mono.Cecil.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Mono.TextTemplating.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NETCore.MailKit.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Newtonsoft.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NLog.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NLog.Extensions.Logging.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NLog.Web.AspNetCore.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Common.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Configuration.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.DependencyResolver.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Frameworks.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.LibraryModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Packaging.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.ProjectModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Protocol.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NuGet.Versioning.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Omnibasis.GoogleWallet.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Parlot.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\PdfSharpCore.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Pipelines.Sockets.Unofficial.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\HtmlRenderer.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\HtmlRenderer.PdfSharp.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\BouncyCastle.Crypto.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\QRCoder.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\RestSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\SixLabors.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\SixLabors.Fonts.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\SixLabors.ImageSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\SkiaSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\StackExchange.Redis.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Swashbuckle.AspNetCore.Annotations.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.ClientModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.CodeDom.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Composition.AttributedModel.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Composition.Convention.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Composition.Hosting.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Composition.Runtime.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Composition.TypedParts.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Data.Odbc.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Drawing.Common.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Private.Windows.Core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Formats.Asn1.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.IO.Pipelines.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Management.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Memory.Data.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Reflection.MetadataLoadContext.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Resources.Extensions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Runtime.Caching.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Text.Encodings.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\System.Text.Json.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\TimeZoneConverter.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\xunit.abstractions.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\xunit.core.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\af\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ar\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\az\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\bg\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\bn-BD\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\da\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\el\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fa\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fi-FI\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr-BE\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\he\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\hr\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\hu\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\hy\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\id\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\is\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko-KR\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ku\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\lv\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ms-MY\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\mt\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\nb\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\nb-NO\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\nl\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ro\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\sk\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\sl\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\sr\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\sr-Latn\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\sv\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\th-TH\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\uk\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\uz-Cyrl-UZ\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\uz-Latn-UZ\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\vi\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-CN\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Humanizer.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Features.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\de\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\es\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\it\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Win32.SystemEvents.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-arm64\native\sni.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x64\native\sni.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x86\native\sni.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\linux-arm\native\libSkiaSharp.so
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\linux-arm64\native\libSkiaSharp.so
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\linux-x64\native\libSkiaSharp.so
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\osx\native\libSkiaSharp.dylib
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-arm64\native\libSkiaSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x64\native\libSkiaSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win-x86\native\libSkiaSharp.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\unix\lib\net8.0\System.Data.Odbc.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Data.Odbc.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\unix\lib\net8.0\System.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Data.SqlClient.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net7.0\System.Management.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.BLL.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Data.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.DTO.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Razor.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Utilities.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.BLL.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.DTO.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Razor.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Utilities.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\Core.Themis.Libraries.Data.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.csproj.AssemblyReference.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.GeneratedMSBuildEditorConfig.editorconfig
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.AssemblyInfoInputs.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.AssemblyInfo.cs
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.csproj.CoreCompileInputs.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.MvcApplicationPartsAssemblyInfo.cs
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.MvcApplicationPartsAssemblyInfo.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.RazorAssemblyInfo.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.RazorAssemblyInfo.cs
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\scopedcss\bundle\Core.Themis.Widgets.Offers.styles.css
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets.build.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets.development.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets.build.endpoints.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets\msbuild.Core.Themis.Widgets.Offers.Microsoft.AspNetCore.StaticWebAssets.props
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets\msbuild.Core.Themis.Widgets.Offers.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets\msbuild.build.Core.Themis.Widgets.Offers.props
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.Core.Themis.Widgets.Offers.props
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.Core.Themis.Widgets.Offers.props
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets.pack.json
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.The.0C522DA8.Up2Date
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\refint\Core.Themis.Widgets.Offers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.pdb
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\Core.Themis.Widgets.Offers.genruntimeconfig.cache
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\obj\Debug\net8.0\ref\Core.Themis.Widgets.Offers.dll
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\GestionPlaces\GetAllAdhesionsByEventIdAndSessionId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\InsertInfoCompForEmailsGroup.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Maquettes\GetVisibleWebMaquettes.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Offers\getOffersByIdentitePA.146.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Offers\getOffersByIdentitePA.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Offers\GetOffersLinkedToBuyerProfilRevendeur.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductColorsAvailable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetAllProductSizesAvailable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetCatalogProductsByFilter.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductColorsAvailableByProductFamilyIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductColorsAvailableByProductSubFamilyIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductSizesAvailableByProductFamilyIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Products\GetProductSizesAvailableByProductSubFamilyIds.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Session\GetSessionsByFormulaId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\Translations\Rodrigue\checkTranslationsFieldVariable.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\WT\Baskets\GetAllOrderIdByBuyerProfilIdAndSessionId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\infoComp\GetVisibleInfoCompGroupsOrderedByPrefAffichage.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\libraries_SQLScripts\ValeurTarifStock\GetBySessionAndCategsId.sql
D:\WORK\CORE_DEV\Themis_core_DEV\Widgets\Core.Themis.Widgets.Offers\Core.Themis.Widgets.Offers\bin\Debug\net8.0\NUglify.dll
