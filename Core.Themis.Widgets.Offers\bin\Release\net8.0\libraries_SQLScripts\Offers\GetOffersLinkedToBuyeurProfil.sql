--DECLARE @pBuyerProfilId INT = 0

IF(@pBuyerProfilId = 0)
BEGIN
	SELECT o.* FROM offre_profil_acheteur opa
	INNER JOIN offre o ON opa.offre_id = o.offre_id
	INNER JOIN profil_acheteur pa ON pa.id = opa.profil_acheteur_id
	INNER JOIN mode_paiement_of_profil_acheteur m ON m.profil_acheteur_id = pa.id
	WHERE is_revendeur = 1 
	AND paiement_operateur_id > 0
END
ELSE
BEGIN
	SELECT o.* FROM offre_profil_acheteur opa
	INNER JOIN offre o ON opa.offre_id = o.offre_id
	INNER JOIN profil_acheteur pa ON pa.id = opa.profil_acheteur_id
	INNER JOIN mode_paiement_of_profil_acheteur m ON m.profil_acheteur_id = pa.id
	WHERE is_revendeur = 1 
	AND paiement_operateur_id > 0
	AND pa.id = @pBuyerProfilId
END