-- DECLARE @pManifId INT = 24
-- DECLARE @pSeanceId INT = 26
-- DECLARE @pEntreeId INT = 883
-- DECLARE @pIdentiteId INT = 11

DECLARE @sql NVARCHAR(MAX) = 
'SELECT *
FROM entree_' + CAST(@pManifId AS varchar) + ' e

---- Jointures ----

INNER JOIN recette r ON e.entree_id = r.entree_id
INNER JOIN commande_ligne cl 
	ON r.manifestation_id = cl.manifestation_id
	AND r.seance_id = cl.seance_id
	AND r.dossier_id = cl.dossier_id
INNER JOIN identite icl ON cl.identite_id = icl.identite_id
INNER JOIN commande c ON cl.commande_id = c.commande_id
INNER JOIN identite ic ON c.identite_id = ic.identite_id
INNER JOIN seance s ON e.seance_id = s.seance_Id
INNER JOIN lieu l ON s.lieu_id = l.lieu_id
INNER JOIN manifestation m ON s.manifestation_id = m.manifestation_id
INNER JOIN structure st ON m.structure_id = st.structure_id
INNER JOIN reference_lieu_physique rlp ON e.reference_unique_physique_id = rlp.ref_uniq_phy_id
INNER JOIN etage et ON rlp.etage_id = et.etage_id
INNER JOIN section sec ON rlp.section_id = sec.section_id
INNER JOIN zone z ON sec.zone_id = z.zone_id
INNER JOIN type_tarif tt ON e.type_tarif_id = tt.type_tarif_id
LEFT JOIN gestion_place gp 
	ON s.seance_id = gp.seance_id  
	AND m.manifestation_id = gp.manif_id
	AND e.type_tarif_id = gp.type_tarif_id 
	AND e.categorie_id = gp.categ_id

---- Conditions ----

WHERE r.manifestation_id = ' + CAST(@pManifId AS varchar) + '
AND e.seance_id = ' + CAST(@pSeanceId AS varchar) + '
AND e.entree_id = ' + CAST(@pEntreeId AS varchar) + '
AND NOT EXISTS (SELECT * 
	FROM recette r
	WHERE r.manifestation_id = ' + CAST(@pManifId AS varchar) + '
	AND r.seance_id = ' + CAST(@pSeanceId AS varchar) + '
	AND r.entree_id = ' + CAST(@pEntreeId AS varchar) + '
	AND r.type_operation = ''A'')
AND (cl.identite_id = ' + CAST(@pIdentiteId AS varchar) + ' OR c.identite_id = ' + CAST(@pIdentiteId AS varchar) + ')'

EXEC(@sql)