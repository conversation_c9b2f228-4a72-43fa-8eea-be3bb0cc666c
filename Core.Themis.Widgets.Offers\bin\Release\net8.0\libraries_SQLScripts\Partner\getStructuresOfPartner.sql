﻿/*
declare @partnerId int
set @partnerId = 1008
*/

declare @partner_structure_id int

/*
SELECT s.structure_id, s.name FROM structureAcceptPartenaire sp 
 INNER JOIN structures s on s.structure_id = sp.structure_id WHERE (sp.date_supp is null or sp.date_supp>getdate()) 
 AND (s.date_supp is null or s.date_supp > getdate()) AND partenaire_id = @partnerId
 */
 
SELECT @partner_structure_id = sp.structure_id FROM structureAcceptPartenaire sp WHERE  partenaire_id = @partnerId

if(@partner_structure_id = 0000)
BEGIN

SELECT s.structure_id, s.name FROM structureAcceptPartenaire sp 
 INNER JOIN structures s on s.structure_id = sp.structure_id WHERE (sp.date_supp is null or sp.date_supp>getdate()) 
 AND partenaire_id = @partnerId
END
ELSE
BEGIN


SELECT s.structure_id, s.name FROM structureAcceptPartenaire sp 
 INNER JOIN structures s on s.structure_id = sp.structure_id WHERE (sp.date_supp is null or sp.date_supp>getdate()) 
 AND (s.date_supp is null or s.date_supp > getdate()) AND partenaire_id = @partnerId
END


