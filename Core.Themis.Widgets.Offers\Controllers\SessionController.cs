﻿using Core.Themis.Libraries.BLL.CarnetTickets;
using Core.Themis.Libraries.BLL.CarnetTickets.Interfaces;
using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Services;
using Core.Themis.Libraries.BLL.Services.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Cache.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Files;
using Core.Themis.Libraries.Utilities.Helpers.Widgets;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.Models;
using Core.Themis.Widgets.Offers.Models;
using Core.Themis.Widgets.Offers.Models.JsonModels;
using Core.Themis.Widgets.Offers.ViewModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class SessionController : Controller
    {

        [BindProperty]
        public CategPriceViewModel CategPriceVM { get; set; }

        private static readonly RodrigueNLogger Logger = new();

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheSHelper _cacheHelper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IGestionTraceManager _gestionTrace;
        private readonly ISponsorManager _sponsorManager;
        private readonly IEventManager _eventManager;
        private readonly ISessionManager _sessionManager;
        private readonly IPartnerManager _partnerManager;
        private readonly ITranslateManager _translateManager;

        private readonly IBasketManager _basketManager;
        private readonly IPriceManager _priceManager;
        private readonly IFeedBooksManager _feedbookManager;
        private readonly IGestionPlaceManager _gestionPlaceManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IReservesManager _reservesManager;

        public SessionController(
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            IBasketManager basketManager,
            IMemoryCache memoryCache,
            ICacheSHelper cachHelper,
            IGestionTraceManager gestionTraceManager,
            ISponsorManager sponsorManager,
            IEventManager eventManager,
            ISessionManager sessionManager,
            IPartnerManager partnerManager,
            ITranslateManager translateManager,
            IPriceManager priceManager,
            IFeedBooksManager feedbookManager,
            // IFeedBooksTarifsManager feedbookTarifManager,

            IGestionPlaceManager gestionPlaceManager,
            IBuyerProfilManager buyerProfilManager,
            IReservesManager reservesManager
           )
        {
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _memoryCache = memoryCache;
            _cacheHelper = cachHelper;
            _basketManager = basketManager;
            _gestionTrace = gestionTraceManager;
            _sponsorManager = sponsorManager;
            _eventManager = eventManager;
            _sessionManager = sessionManager;
            _partnerManager = partnerManager;
            _translateManager = translateManager;
            _priceManager = priceManager;
            _feedbookManager = feedbookManager;
            //_feedbookTarifsManager = feedbookTarifManager;
            _gestionPlaceManager = gestionPlaceManager;
            _buyerProfilManager = buyerProfilManager;
            _reservesManager = reservesManager;
        }
        public IActionResult Index()
        {
            return View();
        }


        [Route("Session/")]
        public IActionResult Session(int structureId, string langCode, int eventId, int identityId, int webUserId, int buyerProfilId, int forceSession, string forceDate, string partnerName, string htmlSelector, string mySettings ="")
        {
            string messageLog = $"Session ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate}, {partnerName}, {htmlSelector}, {mySettings} {RodrigueHttpContext.DomainReferrerUrl})...";

            Logger.Info(structureId, messageLog);

            try
            {
                PartnerDTO? partner = _partnerManager.GetPartnerInfosByName(partnerName);

                if (partner == null)
                    return BadRequest("Partner doesn't exist");

                string widgetSignature = HttpContext.Request.Headers["Signature"]!;

                if (widgetSignature == null)
                    return BadRequest("signature is missing");

                Logger.Debug(structureId, $"Session.signature ={widgetSignature}");

                string partnerToken = WidgetSecurityHelper.GeneratePartnerToken(structureId, partner.PartnerName, partner.SecretKey);

                List<string> lstParamsToHash = new()
                {
                    structureId.ToString(),
                    langCode,
                    partnerName,
                    partnerToken,
                    HttpUtility.HtmlDecode(widgetSignature)
                };

                foreach (var item in lstParamsToHash)
                {
                    Logger.Trace(structureId, $"lstParamsToHash.item ={item}");
                }

                string cacheKeyForSettings = _cacheHelper.SetCache(mySettings, 400);

                string hash = WidgetSecurityHelper.GenerateHash(partner.SecretKey, lstParamsToHash);

                string queryHash = WidgetSecurityHelper.GenerateQueryHash(HttpUtility.HtmlDecode(widgetSignature), partnerToken, partnerName);

                Logger.Trace(structureId, $"Session ({widgetSignature}, {partnerToken}, {partnerName} => {queryHash}");

                //GenerateQueryHash

                string action = RouteData.Values["action"]?.ToString()!;
                string callSeatsSelectionHash = $"{action}Hash/{structureId}/{langCode}/{eventId}/{identityId}/{webUserId}/{buyerProfilId}/{forceSession}/{forceDate}/?htmlSelector={htmlSelector}&hash={hash}&queryHash={queryHash}&mySettingsCacheName={cacheKeyForSettings}";

                Logger.Info(structureId, $"Session ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate}, {partnerName}, {htmlSelector}) ok");
                Logger.Trace(structureId, $"Session ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate}, {partnerName}, {htmlSelector}) ok : {callSeatsSelectionHash}");

                return Json(new { responseText = callSeatsSelectionHash });
            }
            catch(Exception ex ) {
                Logger.Error(structureId, messageLog + " :" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }


        /// <summary>
        /// methode principale "fchoixseance"
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="forcedSession">seance préselectionnée</param>
        /// <param name="forcedDate">date de séance préselectionnée</param>        
        /// <param name="htmlSelector"></param>
        /// <param name="hash"></param>
        /// <param name="queryHash"></param>
        /// <returns></returns>
        [Route("SessionHash/{structureId}/{langCode}/{eventId}/{identityId}/{webUserId}/{buyerProfilId}/{forcedSession}/{forcedDate}")]
        public async Task<IActionResult> SessionHash(int structureId, string langCode, int eventId, int identityId, int webUserId, int buyerProfilId, int forcedSession, string forcedDate, 
            string htmlSelector, string hash, string queryHash, string mySettingsCacheName = "")
        {

            var decodedSettings = System.Web.HttpUtility.HtmlDecode(mySettingsCacheName);

            Logger.Info(structureId, $"SessionHash ({structureId}, langue:{langCode}, manif:{eventId}, identite:{identityId}, webuser:{webUserId}, pa:{buyerProfilId}, fs:{forcedSession}, fd:{forcedDate}, h:{hash}, qh:{queryHash}, ms:{mySettingsCacheName})");

            try
            {

                string typeRun = _configuration["TypeRun"]!;
                SecurityInfos securityData = WidgetSecurityHelper.GetSecurityInfos(queryHash);

                PartnerDTO? partnerInDB = _partnerManager.GetPartnerInfosByName(securityData.PartnerName);

                if (partnerInDB is null)
                    return BadRequest(new { messagr = "Partner doesn't exist" });

                List<string> lstParamsToHash = new()
                {
                    structureId.ToString(),
                    langCode,
                    partnerInDB.PartnerName,
                    securityData.RequestPartnerToken,
                    securityData.RequestWidgetSignature
                };

                if (!WidgetSecurityHelper.CheckAccessAuthorized(hash, queryHash, lstParamsToHash, partnerInDB.SecretKey))
                {
                    Logger.Error(structureId, $"Widget reload problem: Erreur autorisation. hash: {hash}, queryHash: {queryHash}, lstParamsToHash: {string.Join(" / ", lstParamsToHash)}");
                    return Unauthorized();
                }

                string toHash = $"{RodrigueHttpContext.SousWidgetsUrl}${Request.Method}";

                Logger.Info(structureId, $"SessionHash ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, h, qh) toHash = {toHash}");
                Logger.Info(structureId, $"SessionHash ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {hash}, {queryHash}) partnerId = {partnerInDB.PartnerId}");

                string signatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, partnerInDB.SecretKey);

                Logger.Info(structureId, $"SessionHash ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {hash}, {queryHash}) signatureCalculee GET = {signatureCalculee}");

                //Appel la requête IndivSession qui ramène une liste de séances, la grille de tarif et la dispo
                string sessionsListCache = $"sessionsListCache_{structureId}_{identityId}_{eventId}_{langCode}_{forcedSession}_{forcedDate}";

                EventDTO eventsList = null;

                try
                {
                    _memoryCache.TryGetValue(sessionsListCache, out eventsList);

                    if (eventsList == null)
                    {
                        //eventsList = _eventManager.GetEventSessions(structureId, langCode, eventId, identityId, buyerProfilId, DateTime.Now, DateTime.Now.AddYears(10));
                        eventsList = _eventManager.GetEventSessionsDTO(structureId, langCode, eventId, identityId, buyerProfilId, DateTime.Now, DateTime.Now.AddYears(10));

                        if (eventsList != null)
                        {
                            //Mets en cache 
                            var cacheExpiryOptions = new MemoryCacheEntryOptions
                            {
                                AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SessionsAbsoluteExpiration"]!)),
                                Priority = CacheItemPriority.High,
                                SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SessionsSlidingExpiration"]!))
                            };
                            _memoryCache.Set(sessionsListCache, eventsList, cacheExpiryOptions);
                        }
                    }
                }
                catch
                {
                    throw;
                }

                if (eventsList == null || eventsList.ListSessions.Count == 0)
                {
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Aucune remontée de la requête 'getIndivSessionsByIdentitePA pour le calendrier eventId:{eventId} buyer profil:{buyerProfilId} identiteId{identityId}");
                    Logger.Error(structureId, $"Aucune remontée de la requête 'calendrier : seances, grille de tarif, dispo de gestion place' ");

                    var error = new ErrorViewModel
                    {
                        IpAddress = RodrigueHttpContext.IpAddress,
                        Code = (int)HttpStatusCode.NotFound,
                        Message = "no rows for getIndivSessionsByIdentitePA",
                        Date = DateTime.Now,
                        HtmlSelector = htmlSelector
                    };


                    return View("Error", error);
                }

                CategPriceVM = new CategPriceViewModel()
                {
                    EventId = eventId,
                    Sessions = eventsList.ListSessions,
                    Zones = new List<ZoneDTO>(),
                    Floors = new List<FloorDTO>(),
                    Sections = new List<SectionDTO>(),
                    Categories = new List<CategoryDTO>(),
                    SessionIdSelected = 0
                };

                BasketDTO basket = new BasketDTO();

                var listBasket = _basketManager.GetAllBasketInfo(structureId, 0, webUserId);
                if (listBasket != null && listBasket.Count > 0)
                {
                    basket = listBasket[0];
                }

                if (basket.Etat == "I")
                {
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Panier etat I {basket.Etat}");

                    ViewBag.Basket = new BasketDTO();
                }
                else
                {
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Panier {basket.BasketId}");
                    ViewBag.Basket = basket;
                }

                ViewBag.HtmlSelector = htmlSelector;
                ViewBag.SignatureWidgetGet = signatureCalculee;
                ViewBag.WCatalogUrl = _configuration["WidgetCatalogUrl"]!;
                ViewBag.WOfferUrl = _configuration["WidgetOfferUrl"]!;
                ViewBag.WCustomerUrl = _configuration["WidgetCustomerUrl"]!;
                ViewBag.ApplicationPath = HttpContext.Request.PathBase;
                ViewBag.StructureId = structureId;
                ViewBag.EventId = eventId;
                ViewBag.IdentityId = identityId;
                ViewBag.WebUserId = webUserId;
                ViewBag.BuyerProfilId = buyerProfilId;
                ViewBag.PartnerToken = securityData.RequestPartnerToken;
                ViewBag.LangCode = langCode;
                ViewBag.DeviseCode = WidgetUtilitiesHelper.GetDeviseCode(structureId);
                //ViewBag.mySettings = JsonConvert.DeserializeObject(decodedSettings);

                // ViewBag.mySettings = ;


                try
                {
                    var translationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);
                    ViewBag.TranslationsList = translationsList;
                }
                catch (Exception ex)
                {
                    var error = new ErrorViewModel
                    {
                        IpAddress = RodrigueHttpContext.IpAddress,
                        Code = (int)HttpStatusCode.InternalServerError,
                        Message = ex.Message,
                        Date = DateTime.Now,
                        HtmlSelector = htmlSelector
                    };

                    return View("Error", error);
                }


                dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");


                if (mySettingsCacheName != "notSet")
                {
                    string mySettings;
                    if (_memoryCache.TryGetValue(mySettingsCacheName, out mySettings))
                    {
                        try
                        {
                            settingsMerged = WidgetUtilitiesHelper.MergeSettings(settingsMerged, JsonConvert.DeserializeObject<dynamic>(mySettings ?? ""));
                        }
                        catch (Exception ex) // client envoie un truc mal formaté, passer outre
                        {
                            Logger.Error(structureId, $"error {mySettings} : {ex.Message} {ex.StackTrace}");
                        }
                    }

                }



                ViewBag.SettingsMerge = settingsMerged ?? "";

                List<CalendarDayJson> lstCalendarDays = new();
                List<CalendarSessionsJson> lstCalendarSessions = new();

                lstCalendarSessions = eventsList.ListSessions.Select(sess => new CalendarSessionsJson()
                {
                    Date = sess.SessionStartDate.Date.ToString("yyyy-MM-dd"),
                    Hour = sess.SessionStartDate.Hour,
                    Minute = sess.SessionStartDate.Minute,
                    Dispo = sess.AvailablesSeatsCount,
                    EventIsLock = sess.EventIsLock,
                    SessionIsLock = sess.SessionIsLock,
                    SessionIsLockMessage = sess.SessionIsLockMessage,
                    EventIsLockMessage = sess.EventIsLockMessage,

                    SessionId = sess.SessionId,
                    LieuId = sess.Place.PlaceId,
                    LieuName = sess.Place.PlaceName
                }).ToList();

                lstCalendarDays = eventsList.ListSessions
                    .GroupBy(gb => gb.SessionStartDate.Date.ToString("yyyy-MM-dd"))
                    .Select(sess => new CalendarDayJson()
                    {
                        date = sess.Key,
                        available = sess.Any(sess2 => sess2.AvailablesSeatsCount > 0 && !sess2.SessionIsLock && !sess2.EventIsLock),
                        LstSessions = lstCalendarSessions.Where(sess2 => sess2.Date == sess.Key).Select(sess2 => sess2).ToList(),
                        value = JsonConvert.SerializeObject(lstCalendarSessions.Where(sess2 => sess2.Date == sess.Key).Select(sess2 => sess2).ToList()).ToString()
                    }).ToList();

                _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge les séances dans le calendrier");

                ViewBag.SessionsData = JsonConvert.SerializeObject(lstCalendarDays);
                ViewBag.ForceDate = forcedDate;
                ViewBag.ForceSession = forcedSession;
                ViewBag.CustomPackageUrl = WidgetUtilitiesHelper.GetUrlCustomPackage(structureId);

                return View("Index", CategPriceVM);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"SessionHash ({structureId}/{langCode}/{eventId}/{identityId}/{webUserId}/{buyerProfilId}/{forcedSession}/{forcedDate}): {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"SeatsPlanAjax {ex.Message} {ex.StackTrace}");
                return Problem(detail: $"SessionHash:{DateTime.Now.ToString("o")}", title: "SessionHash error");
            }

        }

        /// <summary>
        /// Changement de la séance => refresh zone etage section
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [Route("RefreshZoneFloorSectionAjax/{structureId}/{langCode}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}")]
        public async Task<IActionResult> RefreshZoneFloorSectionAjax(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId, string token)
        {
  
            try
            {

                int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

                if (!partnerIdToken.HasValue)
                {
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Token invalide {token}");
                    return Problem(title: "Invalid token", detail: $"{token.Substring(0, 5)}... is invalid");
                }
                else
                {

                    _gestionTrace.WriteLogMessage(structureId, webUserId, $"RefreshZoneFloorSectionAjax", TypeLog.LoadPage);

                    ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;

                    BasketDTO myBask = new BasketDTO() { BasketId = 0 };
                    List<BasketDTO> lbask = _basketManager.GetAllBasketInfo(structureId, 0, webUserId);
                    if (lbask != null && lbask.Count > 0)
                    {
                        List<BasketDTO> listbaskPV =  lbask.Where(p => p.Etat == "C" || p.Etat == "P").ToList();
                        if (listbaskPV.Count > 0)
                            myBask = listbaskPV.FirstOrDefault();
                    }                        

                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge la liste des categs/tarifs eventId:{eventId} seance:{sessionId} identityId:{identityId} buyerProfilId:{buyerProfilId} panierid:{myBask.BasketId}");

                    List<EventDTO> listEvent = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, "", "", myBask.BasketId);
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"grille de tarif {listEvent.Count} event(s)");

                    if (listEvent.Count == 0)
                    {
                        _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"pb dans le chargement de la liste des Categs/Tarifs {listEvent.Count}");
                        //Todo gérer l'erreur
                        Logger.Error(structureId, $"nb listEvent {listEvent.Count}");
                        throw new Exception("pb dans le chargement de la liste des Categs/Tarifs");
                    }

                    var session = listEvent[0].ListSessions.Where(s => s.SessionId == sessionId).FirstOrDefault();
                    if (session == null)
                    {
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Construit un nouvel objet de séances");
                        CategPriceVM = new CategPriceViewModel()
                        {
                            IsZapperZoneEtageSection = listEvent[0].IsZapperZoneEtageSectionIndiv,
                            IsZapperZoneEtage = listEvent[0].IsZapperZoneEtageIndiv,
                            EventId = eventId,
                            Session = new SessionDTO(),
                            Zones = new List<ZoneDTO>(),
                            Floors = new List<FloorDTO>(),
                            Sections = new List<SectionDTO>(),
                            Categories = new List<CategoryDTO>()
                        };
                    }
                    else
                    {
                        var zones = session.ListZones;
                        var floors = zones.SelectMany(f => f.ListFloors).ToList();
                        var floorsVisu = floors.GroupBy(sec => sec.FloorId).Select(s => s.FirstOrDefault()).ToList();
                        var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                        var sectionsVisu = sections.GroupBy(sec => sec.SectionId).Select(s => s.FirstOrDefault()).ToList();
                        var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                        var listgp = categories.Distinct().ToList().SelectMany(c => c.ListGestionPlace).Distinct().ToList();

                        CategPriceVM = new CategPriceViewModel()
                        {
                            IsZapperZoneEtageSection = listEvent[0].IsZapperZoneEtageSectionIndiv,
                            IsZapperZoneEtage = listEvent[0].IsZapperZoneEtageIndiv,
                            EventId = eventId,
                            Session = session,
                            Zones = zones,
                            Floors = floorsVisu,
                            Sections = sectionsVisu,
                            Categories = categories,
                            GestionPlaces = listgp
                        };

                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Construit l'objet avec les Zones, étages, sections, catégories, gestion places");
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Manifestation en zapper ZES {CategPriceVM.IsZapperZoneEtageSection}, Zapper Zone Etage {CategPriceVM.IsZapperZoneEtage}");
                    }

                    ViewBag.Categories = CategPriceVM.Categories.Select(c => c.CategId).ToArray();
                    ViewBag.APIToken = token;

                    //Récupère la liste des traductions
                    var translationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);
                    ViewBag.TranslationsList = translationsList;

                    dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");

                    ViewBag.SettingsMerge = (settingsMerged != null) ? settingsMerged : "";
                    ViewBag.LangCode = langCode;
                    ViewBag.StructureId = structureId;

                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"retour des liste zones, étages, sections");

                    return PartialView("_ZoneFloorSection", CategPriceVM);
                }

            }

            catch (Exception ex)
            {
                Logger.Error(structureId, $"RefreshZoneFloorSectionAjax({structureId}/{langCode}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}): {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"RefreshZoneFloorSectionAjax {ex.Message} {ex.StackTrace}");
                return Problem(detail: $"RefreshZoneFloorSectionAjax:{DateTime.Now.ToString("o")}", title: "SessionHash error");
            }
        }

        /// <summary>
        /// Changement zone etage section => refresh grille tarif
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="zoneId"></param>
        /// <param name="floorId"></param>
        /// <param name="sectionId"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [Route("RefreshGrilleTarifAjax/{structureId}/{langCode}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{zoneId}/{floorId}/{sectionId}")]
        [HttpPost]
        public async Task<IActionResult> RefreshGrilleTarifAjax(int structureId, string langCode, int eventId, int sessionId, int identityId,
            int webUserId, int buyerProfilId, int zoneId, int floorId, int sectionId, string tab, List<int> categsId, string token)
        {
            int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

            try
            {
                if (!partnerIdToken.HasValue)
                {
                    return Problem("Invalid token");
                }
                else
                {
                    BasketDTO myBask = new BasketDTO() { BasketId = 0 };
                    List<BasketDTO> lbask = _basketManager.GetAllBasketInfo(structureId, 0, webUserId);
                    if (lbask != null && lbask.Count > 0)
                    {
                        List<BasketDTO> listbaskPV = lbask.Where(p => p.Etat == "C" || p.Etat == "P").ToList();
                        if (listbaskPV.Count > 0)
                            myBask = listbaskPV.FirstOrDefault();
                    }

                    ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;

                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge la liste des categs/tarifs eventId:{eventId} seance:{sessionId} identityId:{identityId} buyerProfilId:{buyerProfilId} panierid:{myBask.BasketId}");
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge la grille tarifs manager LoadPricesGrid cache de 1 min et 4 secondes si un panier");
                    Logger.Debug(structureId, $"Charge la grille tarifs manager LoadPricesGrid {langCode} {eventId} {sessionId} {webUserId} {buyerProfilId} {myBask.BasketId}");
                    List<EventDTO> eventsCategsPricesList = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, "", "", myBask.BasketId);

                    if (eventsCategsPricesList.Count == 0)
                    {
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"La grille de tarif ne remonte aucune ligne lang:{langCode}, event{eventId}, session:{sessionId}, buyerprofil:{buyerProfilId}, basketId:{myBask.BasketId}");
                        Logger.Error(structureId, $"La grille de tarif ne remonte aucune ligne lang:{langCode}, event{eventId}, session:{sessionId}, buyerprofil:{buyerProfilId}, basketId:{myBask.BasketId}");
                        throw new Exception();
                    }

                    var session = eventsCategsPricesList[0].ListSessions.Where(s => s.SessionId == sessionId).FirstOrDefault();

                    if (session == null)
                    {

                        CategPriceVM = new CategPriceViewModel()
                        {
                            Session = new SessionDTO(),
                            Zones = new List<ZoneDTO>(),
                            Floors = new List<FloorDTO>(),
                            Sections = new List<SectionDTO>(),
                            Categories = new List<CategoryDTO>(),
                            SessionIdSelected = sessionId,
                            ZoneIdSelected = zoneId,
                            FloorIdSelected = floorId,
                            SectionIdSelected = sectionId
                        };
                    }
                    else
                    {


                        var zones = session.ListZones;
                        if (zoneId > 0)
                        {
                            zones = zones.Where(z => z.ZoneId == zoneId).ToList();
                        }

                        var floors = zones.SelectMany(f => f.ListFloors).ToList();
                        if (floorId > 0)
                        {
                            floors = floors.Where(f => f.FloorId == floorId).ToList();
                        }
                        var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                        if (sectionId > 0)
                        {
                            sections = sections.Where(s => s.SectionId == sectionId).ToList();
                        }


                        List<CategoryDTO> categories = sections.Distinct().SelectMany(s => s.ListCategories)
                                .OrderBy(c => c.DisplayRank)
                                .ThenByDescending(c => c.Dispo)// )=> ordonner by max(dispo) => la categ resultant doit avoir le max dispo de toutes les sections CAS-85767-V8B9D0
                                .GroupBy(c => c.CategId)
                                .Select(gp =>
                                {
                                    var categ = gp.First();
                                    categ.ListPrices = gp.SelectMany(c => c.ListPrices).DistinctBy(p => p.PriceId).ToList();
                                    categ.ListGestionPlace = gp.SelectMany(c => c.ListGestionPlace).DistinctBy(gestp => gestp.GestionPlaceId).OrderBy(p => p.Price.DisplayRank).ToList();

                                    return categ;
                                })
                                .ToList();

                      
                        if (categsId.Count > 0) // categs choisie par l'internaute
                        {
                            categories = categories.Where(c => categsId.Any(ca => ca == c.CategId)).ToList();
                        }

                        try
                        {
                            var sponsors = _sponsorManager.GetSponsors(structureId);

                        }
                        catch (Exception ex)
                        {
                            _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Erreur dans l'appel get sponsor {ex.Message}");
                            Logger.Error(structureId, $"{ex.Message} - {ex.StackTrace}");
                            throw;
                        }

                        var gestionPlaces = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Where(g => g.IsSurPlan).ToList();
                        if (tab == "auto")
                        {
                            gestionPlaces = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Where(g => g.IsAutomatique).ToList();
                        }

                        var gestionPlacesId = gestionPlaces.Select(gp => gp.GestionPlaceId).Distinct().ToList();
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge le commentaire pour chaque tarifs de ces GP {string.Join(',', gestionPlacesId)}");

                        // var commentsTarifList = await ApiOffersHelper.GetListPricesComments(apiOffersUrl, token, structureId, gestionPlacesId);
                        var commentsTarifList = _gestionPlaceManager.LoadTarifCommentaires(structureId, gestionPlacesId);



                        #region carnet de tickets

                        //var listJetonsCarnetTickets = _feedbookManager.getAll(structureId);
                        //var listFeedBook_UsablesPriceId = _feedbookTarifsManager.getUsablesPrices(structureId);

                        var listFeedBook_UsablesPriceId = _feedbookManager.listTarifsLies(structureId);


                        #endregion


                        foreach (var categ in categories)
                        {
                            foreach (var gestionPlace in categ.ListGestionPlace)
                            {
                                if (listFeedBook_UsablesPriceId.Contains(gestionPlace.PriceId))
                                {
                                    gestionPlace.Price.IsPriceCarnetTicket = true;
                                    Logger.Trace(structureId, $"jetons utilisables sur {gestionPlace.PriceId}, {gestionPlace.Price.UnitTTCAmount} c€");
                                }

                                gestionPlace.Price.Sponsor = _sponsorManager.GetSponsorsByPriceId(structureId, gestionPlace.Price.PriceId);

                                foreach (var commentaireTarif in commentsTarifList)
                                {
                                    if (commentaireTarif.TarifId == gestionPlace.Price.PriceId)
                                    {
                                        if (gestionPlace.Price.TarifCommentaires == null)
                                            gestionPlace.Price.TarifCommentaires = new List<CommentaireTarifDTO>();

                                        gestionPlace.Price.TarifCommentaires.Add(commentaireTarif);
                                    }
                                }
                            }
                        }

                        // filtrer sur les categs qui contiennent au moins 1 gestion place de type 'placement auto' ou 'surplan'
                        if (tab == "plan")
                        {
                            categories = categories.Where(c => c.ListGestionPlace.Where(gp => gp.IsSurPlan == true).Any()).ToList();
                            foreach (var categ in categories)
                            {
                                categ.ListGestionPlace = categ.ListGestionPlace.Where(gp => gp.IsSurPlan).ToList();
                            }
                        }
                        if (tab == "auto")
                        {
                            categories = categories.Where(c => c.ListGestionPlace.Where(gp => gp.IsAutomatique == true).Any()).ToList();
                            foreach (var categ in categories)
                            {
                                categ.ListGestionPlace = categ.ListGestionPlace.Where(gp => gp.IsAutomatique).ToList();
                            }
                        }


                        CategPriceVM = new CategPriceViewModel()
                        {
                            Session = session,
                            Zones = zones,
                            Floors = floors,
                            Sections = sections,
                            Categories = categories,
                            SessionIdSelected = sessionId,
                            ZoneIdSelected = zoneId,
                            FloorIdSelected = floorId,
                            SectionIdSelected = sectionId,
                            GestionPlaces = gestionPlaces // n'est pas utilisé ????
                        };


                    }

                    ViewBag.APIToken = token;

                    BuyerProfilDTO buyerProfilInfos = new BuyerProfilDTO();
                    if (buyerProfilId > 0)
                    {
                        //buyerProfilInfos = await ApiOffersHelper.GetViaExterne(apiOffersUrl, token, structureId, buyerProfilId);
                        buyerProfilInfos = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId) ?? new();

                    }

                    dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");

                    ViewBag.SettingsMerge = (settingsMerged != null) ? settingsMerged : "";
                    ViewBag.BuyerProfilInfos = buyerProfilInfos;
                    ViewBag.TranslationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);
                    ViewBag.IdentityId = identityId;
                    ViewBag.WebUserId = webUserId;
                    ViewBag.BuyerProfilId = buyerProfilId;
                    ViewBag.EventId = eventId;
                    ViewBag.Session = session;
                    ViewBag.StructureId = structureId;
                    ViewBag.LangCode = langCode;
                    ViewBag.HtmlSelector = "";

                    return PartialView("_GrilleTarif", CategPriceVM);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"RefreshGrilleTarifAjax({structureId}/{langCode}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{zoneId}/{floorId}/{sectionId}): {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"RefreshGrilleTarifAjax({eventId},{sessionId}):{ex.Message} {ex.StackTrace}");
                return Problem(detail: $"RefreshGrilleTarifAjax:{DateTime.Now.ToString("o")}", title: "RefreshGrilleTarifAjax error");
            }
        }


        /// <summary>
        /// Retourne le plan de salle
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="langCode"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Route("SeatsPlanAjax/{structureId}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{langCode}")]
        public async Task<IActionResult> SeatsPlanAjax(int structureId, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId,
            string langCode, [FromQuery] string token)
        {
            _gestionTrace.WriteLogMessage(structureId, webUserId, $"--- Charge la grille tarif du choix sur plan ---", TypeLog.LoadPage);

            try
            {

                int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

                if (!partnerIdToken.HasValue)
                {
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"Erreur du token {token}");
                    return Problem("Invalid token");
                }
                else
                {
                    ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;

                    BasketDTO myBask = new BasketDTO() { BasketId = 0 };
                    List<BasketDTO> lbask = _basketManager.GetAllBasketInfo(structureId, 0, webUserId);
                    if (lbask != null && lbask.Count > 0)
                        myBask = lbask[0];

                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge la liste des categs/tarifs eventId:{eventId} seance:{sessionId} identityId:{identityId} buyerProfilId:{buyerProfilId} panierid:{myBask.BasketId}");
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge la grille tarifs manager LoadPricesGrid cache de 1 min et 4 secondes si un panier");

                    List<EventDTO> eventsCategsPricesList = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, "", "", myBask.BasketId);
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"grille de tarif {eventsCategsPricesList.Count} event(s)");

                    if (eventsCategsPricesList.Count == 0)
                    {
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"La grille de tarif ne remonte aucune ligne lang:{langCode}, event{eventId}, session:{sessionId}, buyerprofil:{buyerProfilId}, basketId:{myBask.BasketId}");
                        Logger.Error(structureId, $"La grille de tarif ne remonte aucune ligne lang:{langCode}, event{eventId}, session:{sessionId}, buyerprofil:{buyerProfilId}, basketId:{myBask.BasketId}");
                        throw new Exception();
                    }

                    var session = eventsCategsPricesList[0].ListSessions.Where(s => s.SessionId == sessionId).FirstOrDefault();

                    if (session == null)
                    {

                        CategPriceVM = new CategPriceViewModel()
                        {
                            Session = new SessionDTO(),
                            Zones = new List<ZoneDTO>(),
                            Floors = new List<FloorDTO>(),
                            Sections = new List<SectionDTO>(),
                            Categories = new List<CategoryDTO>(),
                            SessionIdSelected = sessionId
                        };
                    }
                    else
                    {
                        var zones = session.ListZones;
                        var floors = zones.SelectMany(f => f.ListFloors).ToList();
                        var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();

                        var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories)
                                .GroupBy(x => x.CategId)
                                .Select(x => x.First())
                                .ToList();

                        var listgp = categories.Distinct().ToList().SelectMany(c => c.ListGestionPlace).Distinct().ToList();


                        CategPriceVM = new CategPriceViewModel()
                        {
                            Session = session,
                            Zones = zones,
                            Floors = floors,
                            Sections = sections,
                            Categories = categories,
                            SessionIdSelected = sessionId,
                            ZoneIdSelected = 0,
                            FloorIdSelected = 0,
                            SectionIdSelected = 0,
                            GestionPlaces = listgp
                        };

                    }

                    dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");

                    ViewBag.SettingsMerge = (settingsMerged != null) ? settingsMerged : "";
                    ViewBag.APIToken = token;
                    ViewBag.TranslationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);
                    ViewBag.IdentityId = identityId;
                    ViewBag.WebUserId = webUserId;
                    ViewBag.BuyerProfilId = buyerProfilId;
                    ViewBag.EventId = eventId;
                    ViewBag.Session = session;
                    ViewBag.StructureId = structureId;
                    ViewBag.LangCode = langCode;
                    ViewBag.HtmlSelector = "";

                    //retour grille de tarif
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Retourne la grille tarifs du choix sur plan");

                    return PartialView("_SeatPlan", CategPriceVM);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"SeatsPlanAjax({structureId}/{eventId}/{sessionId}/{identityId}/{webUserId}/{buyerProfilId}/{langCode}): {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"SeatsPlanAjax {ex.Message} {ex.StackTrace}");
                return Problem(detail: $"SeatsPlanAjax:{DateTime.Now.ToString("o")}", title: "SeatsPlanAjax error");
            }

        }


        /// <summary>
        /// Choix sur plan
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="zoneIds"></param>
        /// <param name="floorIds"></param>
        /// <param name="sectionIds"></param>
        /// <param name="token"></param>
        /// <param name="ctg"></param>
        /// <param name="isViewOnly">voir sur plan uniquement == true</param>
        /// <returns></returns>        
        [Route("Session/ChoiceSeatsPlanAjax/{structureId}/{basketId}/{buyerProfilId}/{identityId}/{langCode}/{eventId}/{sessionId}/{placeId}")]
        [HttpPost]
        public async Task<IActionResult> ChoiceSeatsPlanAjax(int structureId, int basketId, int buyerProfilId, int identityId, string langCode, int eventId, int sessionId, int webUserId,
            List<int> zoneIds, List<int> floorIds,
            List<int> sectionIds, int placeId, string token, List<int> categsId, List<int> listgpidPlan, bool isViewOnly = false)
        {
            try
            {
                BasketDTO basket = new();

                if (basketId > 0)
                {
                    basket = _basketManager.GetBasketById(structureId, basketId);
                    webUserId = basket.WebUser.UserId;


                }

                _gestionTrace.WriteLogMessage(structureId, webUserId, $"Load {buyerProfilId}, {eventId}, {sessionId}, {langCode}", TypeLog.LoadPage);

                Logger.Trace(structureId, $"ChoiceSeatsPlanAjax ({structureId}, {eventId}, {sessionId}, {string.Join(";", zoneIds)},{string.Join(";", floorIds)},{string.Join(";", sectionIds)}, " +
               $"{placeId}, {langCode}, {string.Join(";", categsId)}, {string.Join(";", listgpidPlan)}, {token})...");



                ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;
                var isPlanRodrigue = true;

                //var apiCatalogUrl = _configuration["ApiCatalogUrl"]!;
                string physicalPathSeatsPlanPerspective = _configuration["SeatPlan:SeatingPlanPerspectivePhysicalPath"].ToString();
                physicalPathSeatsPlanPerspective = physicalPathSeatsPlanPerspective.Replace("{structureId}", structureId.ToString("0000")).Replace("[param]", placeId.ToString());
                _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"ChoiceSeatsPlanAjax - XML {physicalPathSeatsPlanPerspective}");
                Logger.Trace(structureId, $"GetSeatsPlan - physicalPathSeatsPlanPerspective  : {physicalPathSeatsPlanPerspective}");


                // check si un fichier de correspondance des coordonnées existe
                string fileCorrespondance = FilesForRequestHelper.GetFile(physicalPathSeatsPlanPerspective, structureId, placeId.ToString());


                if (!string.IsNullOrEmpty(fileCorrespondance))
                {
                    isPlanRodrigue = false;

                    string physicalBackground = FilesForRequestHelper.GetFileBackGroundPlanSalle(physicalPathSeatsPlanPerspective, "plan_location", placeId, eventId, sessionId, zoneIds[0], floorIds[0], sectionIds[0], "_backgroundimage.jpg");

                    Logger.Trace(structureId, $"GetSeatsPlan - physicalBackground : {physicalBackground}");

                    if (!string.IsNullOrEmpty(physicalBackground))
                    {

                        string imgName = Path.GetFileName(physicalBackground);

                        var urlImgBackGroundPlanSalle = _configuration["SeatPlan:SeatingPlanUrlPath"].ToString();
                        string urlBackGround = urlImgBackGroundPlanSalle.Replace("{structureId}", structureId.ToString("0000"));

                        ViewBag.BackgroundUrl = urlBackGround + imgName;
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Chemin physique de l'image {urlBackGround + imgName}");

                        Logger.Trace(structureId, $"GetSeatsPlan - ViewBag.backgroundUrl : {ViewBag.backgroundUrl}");
                    }
                    else
                    {
                        // pas de background => on retourne sur le plan Rodrigue
                        _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Plan du type Rodrigue");

                        isPlanRodrigue = true;
                    }
                }



                dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");

                //Permet de ne pas afficher les traits et autres extra

                //le plan est-il afficher en entier, ou juste la zone/étage/section demandée
                bool showTheWholePlan = false;
                if (settingsMerged.plan.showTheWholePlan != null)
                {
                    if (settingsMerged.plan.showTheWholePlan.ContainsKey(placeId.ToString()))
                    {
                        //si il y a une configuration pour ce lieu
                        showTheWholePlan = settingsMerged.plan.showTheWholePlan[placeId.ToString()].Value;
                    }
                    else if (settingsMerged.plan.showTheWholePlan.ContainsKey("default"))
                    {
                        //si il y a une configuration default
                        showTheWholePlan = settingsMerged.plan.showTheWholePlan["default"].Value;
                    }
                }

                ViewBag.PlaceId = placeId;

                //if ((zoneIds.IndexOf(0) != -1 && floorIds.IndexOf(0) != -1  && sectionIds.IndexOf(0) != -1) || showTheWholePlan) // ??????????????

                ViewBag.TranslationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode);

                bool showExtraPlanObjects = true; // ??
                ViewBag.ShowExtraPlanObjects = showExtraPlanObjects; // ??
                ViewBag.Categories = categsId;
                ViewBag.ReservesId = new List<int>();
                ViewBag.IsViewOnly = isViewOnly;

                if (!isViewOnly && listgpidPlan.Count > 0)
                {
                    Dictionary<int, List<int>> dictReservesByCategs = _reservesManager.LoadCommonsReservesListByCategsId(structureId, listgpidPlan);
                    try
                    {
                        ViewBag.ReservesByCategs = dictReservesByCategs;
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }



                BasketDTO bask = new BasketDTO();
                List<SeatDTO> seats = new List<SeatDTO>();
                List<GestionPlaceDTO> gestionPlaces = new List<GestionPlaceDTO>();
                if (basketId > 0)
                {
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Récupère les informations du panier depuis Rodrigue {basketId}");

                    //bask = await ApiOffersHelper.GetWithOpen(apiOffersUrl, token, structureId, basketId, langCode);

                    bask = _basketManager.GetAllBasketInfo(structureId, basketId, 0).FirstOrDefault();
                    _basketManager.FillFromOpen(structureId, bask, langCode);


                    var events = bask.ListEventsUnitSales.ToList();
                    var sessions = events.SelectMany(s => s.ListSessions).ToList();
                    var zones = sessions.SelectMany(z => z.ListZones).ToList();
                    var floors = zones.SelectMany(f => f.ListFloors).ToList();
                    var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                    var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();

                    var prices = categories.Distinct().ToList().SelectMany(c => c.ListPrices).Distinct().ToList();
                    seats = prices.Distinct().ToList().SelectMany(c => c.ListSeats).Distinct().ToList();
                    gestionPlaces = prices.Distinct().ToList().SelectMany(c => c.ListGestionPlace).Distinct().ToList();
                }

                BuyerProfilDTO buyerProfilInfos = new();
                if (buyerProfilId > 0)
                {
                    //buyerProfilInfos = await ApiOffersHelper.GetViaExterne(apiOffersUrl, token, structureId, buyerProfilId);
                    buyerProfilInfos = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId);

                }
                ViewBag.BuyerProfilInfos = buyerProfilInfos;

                //var priceGrid = await ApiOffersHelper.GetListCategsPrices(apiOffersUrl, token, structureId, eventId, sessionId, langCode, 0, webUserId, buyerProfilId, basketId);
                var priceGrid = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, "", "", basketId);

                _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Grille de tarif sur le choix sur plan {priceGrid.Count}");
                List<CategoryDTO> categoriesFromPriceGrid = new List<CategoryDTO>();

                if (priceGrid.Count > 0)
                {
                    categoriesFromPriceGrid = priceGrid.SelectMany(t => t.ListSessions.SelectMany(s => s.ListZones.SelectMany(z => z.ListFloors.SelectMany(sec => sec.ListSections.SelectMany(gp => gp.ListCategories))))).ToList();
                    ViewBag.categoriesFromPriceGrid = categoriesFromPriceGrid.GroupBy(c => c.CategId).Select(cat => cat.First()).ToList().OrderBy(orank => orank.DisplayRank).OrderBy(ocategid => ocategid.CategId).ToList();
                }
                else
                {
                    ViewBag.categoriesFromPriceGrid = new List<CategoryDTO>();
                }


                var categsPossibleSurPlan = categoriesFromPriceGrid.Where(c => c.ListGestionPlace.Where(gp => gp.IsSurPlan == true).Any());
                ViewBag.CategsPossibleSurPlan = categsPossibleSurPlan.Select(cat => cat.CategId).Distinct().ToList();

                ViewBag.Seats = seats;
                ViewBag.GestionPlaces = gestionPlaces;

                ViewBag.Basket = bask;
                ViewBag.SettingsMerge = (settingsMerged != null) ? settingsMerged : "";

                if (isPlanRodrigue) // plan à plat depuis bdd Rodrigue
                {
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge le plan et les objets autour (Lignes,Textes, poteaux,...) pour le plan Rodrigue");

                    //Task<List<SeatDTO>> listSeatsTask;
                    List<SeatDTO> listSeats;
                    //Task<ListSeatingPlanObjectsViewModel> objTextesPolesTask;
                    ListSeatingPlanObjectsViewModel objTextesPoles;
                    List<TextOrLineSeatingPlanDTO> listTexts;
                    if (showTheWholePlan || (zoneIds.Contains(0) && floorIds.Contains(0) && sectionIds.Contains(0) && zoneIds.Count() == 1 && floorIds.Count() == 1 && sectionIds.Count() == 1))
                    {
                        //listSeatsTask = ApiCatalogHelper.GetSeatsPlan(apiCatalogUrl, token, structureId, langCode, eventId, sessionId, webUserId, 0, 0, 0);
                        listSeats = _sessionManager.LoadSeatPlan(structureId, 0, eventId, sessionId, 0, 0, 0, 0, "", langCode, webUserId);
                        listTexts = _sessionManager.LoadTextsPlan(structureId, 0, eventId, sessionId, 0, 0, 0);

                        Logger.Debug(structureId, $"TextsList({structureId}, {eventId}, {sessionId}, 0, 0, 0) ok");

                        //objTextesPolesTask = ApiCatalogHelper.GetSeatsPlanObjects(apiCatalogUrl, token, structureId, eventId, sessionId);
                        // objTextesPoles = await ApiCatalogHelper.GetSeatsPlanObjects(apiCatalogUrl, token, structureId, eventId, sessionId);
                    }
                    else
                    {
                        //listSeatsTask = ApiCatalogHelper.GetSeatsPlan(apiCatalogUrl, token, structureId, langCode, eventId, sessionId, webUserId, zoneIds, floorIds, sectionIds);
                        listSeats = _sessionManager.LoadSeatPlan(structureId, 0, eventId, sessionId, zoneIds, floorIds, sectionIds, 0, "", langCode, webUserId);
                        //objTextesPolesTask = ApiCatalogHelper.GetSeatsPlanObjects(apiCatalogUrl, token, structureId, eventId, sessionId, zoneIds, floorIds, sectionIds);
                        listTexts = _sessionManager.LoadTextsPlan(structureId, 0, eventId, sessionId, zoneIds, floorIds, sectionIds);

                        // objTextesPoles = await ApiCatalogHelper.GetSeatsPlanObjects(apiCatalogUrl, token, structureId, eventId, sessionId, zoneIds, floorIds, sectionIds);
                    }

                    List<TextShortSeatingPlan> listShortText = listTexts.Where(s => s.Type_Siege == "isTexte").Select(s => (TextShortSeatingPlan)s).ToList();
                    List<TextLongSeatingPlanEntity> listLongText = listTexts.Where(s => s.Type_Siege == "TexteLong").Select(s => (TextLongSeatingPlanEntity)s).ToList();
                    List<LineSeatingPlanEntity> listLines = listTexts.Where(s => s.Type_Siege == "isTrait1" || s.Type_Siege == "isTrait2").Select(s => (LineSeatingPlanEntity)s).ToList();
                    List<PoleSeatingPlanEntity> listPoteaux = listTexts.Where(s => s.Type_Siege == "isPoteau").Select(s => (PoleSeatingPlanEntity)s).ToList();

                    objTextesPoles = new ListSeatingPlanObjectsViewModel()
                    {
                        listLongTexts = listLongText,
                        listShortTexts = listShortText,
                        listPoles = listPoteaux,
                        listLines = listLines
                    };

                    ViewBag.showTheWholePlan = showTheWholePlan;
                    ViewBag.zoneIdsAsked = zoneIds;
                    ViewBag.floorIdsASked = floorIds;
                    ViewBag.sectionIdsAsked = sectionIds;

                    //List<SeatDTO> listSeats = new List<SeatDTO>();
                    //await Task.WhenAll(listSeatsTask, objTextesPolesTask);

                    if (listSeats != null)
                    {
                        ListSeatingPlanObjectsViewModel obContener = objTextesPoles;
                        ViewBag.ListShortTexts = obContener.listShortTexts;
                        ViewBag.ListLongTexts = obContener.listLongTexts;
                        ViewBag.ListLines = obContener.listLines;
                        ViewBag.ListPoles = obContener.listPoles;
                    }
                    else
                    {
                        ViewBag.ListShortTexts = new List<TextShortSeatingPlan>();
                        ViewBag.ListLongTexts = new List<TextLongSeatingPlanEntity>();
                        ViewBag.ListLines = new List<LineSeatingPlanEntity>();
                        ViewBag.ListPoles = new List<PoleSeatingPlanEntity>();
                    }

                    Logger.Trace(structureId, $"ChoiceSeatsPlanAjax : get plan Rodrigue");

                    return PartialView("_SeatPlanRodrigue", listSeats);
                }
                else
                {   // photo de background
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Charge le plan photo");

                    ViewBag.FileXYPerspective = physicalPathSeatsPlanPerspective; // les coordonnées sont dans un fichier xml
                    List<SeatDTO> listSeats = _sessionManager.LoadSeatPlan(structureId, 0, eventId, sessionId, zoneIds, floorIds, sectionIds, 0, "", langCode, webUserId);

                    Logger.Trace(structureId, $"ChoiceSeatsPlanAjax : get plan non Rodrigue (image)");

                    return PartialView("_SeatPlanPhoto", listSeats);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"ChoiceSeatsPlanAjax({structureId}/{basketId}/{buyerProfilId}/{identityId}/{langCode}/{eventId}/{sessionId}/{placeId}): {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"ChoiceSeatsPlanAjax {ex.Message} {ex.StackTrace}");
                return Problem(detail: $"ChoiceSeatsPlanAjax:{DateTime.Now.ToString("o")}", title: "ChoiceSeatsPlanAjax error");
            }
        }

        //[HttpGet]
        //[Route("Session/modalpanierajax")]
        //public IActionResult ModalPanierAjax()
        //{
        //    return PartialView("Modals/Panier");
        //    //passer des infos

        //}


    }
}
