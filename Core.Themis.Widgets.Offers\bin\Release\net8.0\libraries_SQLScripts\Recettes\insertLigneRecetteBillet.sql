﻿/* insertLigneRecetteBillet.sql */
/*

declare @pmanifestation_id int
declare @poperateur_id int
declare @pnumbillet varchar(50)
declare @pentree_id int
declare @ptypesupport varchar(10)
declare @pnumexterne varchar(50)

declare @pemetteur_identifiant varchar(50)
declare @pmaquette_id int

*/
/**** 
******* (si typemaquette de MAQUETTE_BILLET where maquette_id = @pmaquette_id ) ===> 'CARTEWEB' 
*/
declare @ntypeCarte int
SELECT @ntypeCarte = count(*) FROM MAQUETTE_BILLET WHERE TypeMaquette ='CARTEWEB' and Maquette_Billet_ID = @pmaquette_id
IF (@ntypeCarte=1)
BEGIN
	DECLARE @myCmd int

	DECLARE @myAboId int
	SELECT @myAboId = abo_id from Commande_Ligne_comp clc
		inner join commande_ligne cl on clc.commande_ligne_id = cl.commande_ligne_id
		WHERE etat in ('P','B') and cl.seance_id = @pseance_id and cl.dossier_id = @pdossier_id and cl.type_ligne ='DOS' and cl.commande_id = @pcommande_id

	IF (@myAboId > 0)
	BEGIN
	
		/*
		************ existe t-il une recette en type_operation='E' pour cette même entree_id sur une manif differente dans la même commande, dans la même formule  
		-- numero billets differents, code barre identique
		*/
		DECLARE @myCeluiDejaPresentNumBillet int = 0
		DECLARE @myCeluiDejaPresentExterne varchar(50) = ''

		SELECT top 1 @myCeluiDejaPresentNumBillet = r.numbillet, @myCeluiDejaPresentExterne = r.externe from recette r 
		INNER JOIN commande_Ligne_comp clc on r.seance_id = clc.Seance_ID and r.dossier_id = clc.Dossier_ID 
		INNER JOIN commande_ligne cl on clc.commande_ligne_id = cl.commande_ligne_id		
		WHERE type_operation ='E' and entree_id = @pentree_id and cl.type_ligne ='DOS' and cl.commande_id = @pcommande_id

		IF (@myCeluiDejaPresentNumBillet> 0 and @myCeluiDejaPresentExterne<>'')
		BEGIN
			--set @pnumbillet= @myCeluiDejaPresentNumBillet
			SET @pnumexterne = @myCeluiDejaPresentExterne
		END
	END		
END


DECLARE @MyID numeric (18,0);
DECLARE @cbar varchar(max); 

SET @cbar = @pnumexterne

SELECT @cbar = case WHEN (ControleAcces is null OR rtrim(ltrim(controleacces))='') THEN @pnumexterne ELSE controleacces end 
	FROM entree_[MANIFID] WHERE entree_id=@pentree_id

INSERT INTO RECETTE ([manifestation_id],[seance_id] ,[entree_id],[operateur_id],[date_operation],[type_operation],
	[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4],[montant5],[montant6],[montant7],[montant8],[montant9],[montant10],
	[numbillet],[categorie_id],[externe],[type_tarif_id],[dossier_id],[motif])
SELECT @pmanifestation_id, seance_id, entree_id, @poperateur_id,getdate(),'E'
	,'01/01/1900',montant1, montant2, montant3 ,montant4, montant5, montant6, montant7, montant8, montant9, montant10,
	@pnumbillet, categorie_id, @cbar, type_tarif_id, dossier_id,'' FROM ENTREE_[MANIFID]
WHERE entree_id=@pentree_id


SELECT @MyID=SCOPE_IDENTITY();

INSERT INTO RECETTE_COMPLEMENT ([recette_id],[typesupport],[codebarre],[tag_rfid],[emetteur_identifiant],[Maquette_id])
 VALUES (@MyID, @ptypesupport, @cbar,'', @pemetteur_identifiant, @pmaquette_id)

SELECT @MyID as recette_id