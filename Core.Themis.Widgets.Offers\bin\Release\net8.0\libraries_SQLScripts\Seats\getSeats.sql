/* getSeats.sql */
SELECT convert(int,entree_id) as entree_id,
convert(int,e.seance_id) as seance_id,
categ_id, 
cat.categ_nom,
entree_etat,
flag_selection,
rlp.rang,
rlp.siege,
rlp.zone_id,
z.zone_nom, rlp.etage_id, etag.etage_nom, rlp.section_id, sect.section_nom,
denom.denom_nom, rlp.orientation,
rlp.pos_x, rlp.pos_y, rlp.iindex, rlp.type_siege

FROM entree_[eventID] e
INNER JOIN seance s on s.seance_Id = e.seance_id
INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
INNER JOIN categorie cat ON cat.categ_id = e.categorie_id
INNER JOIN reference_lieu_physique rlp ON e.iindex = rlp.iindex and e.reference_unique_physique_id = rlp.ref_uniq_phy_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id
INNER JOIN etage etag ON etag.etage_id = rlp.etage_id
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN denomination denom on denom.denom_id = rlp.denomination_id


WHERE e.entree_id IN ({listSeatsId})