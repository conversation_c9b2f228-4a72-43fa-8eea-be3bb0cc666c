/* getGrilleTarifs.sql */
select seance_id 
into #filtreSeances
FROM seance where seance_id in ({mylistsessions}) and manifestation_id =[eventID]
declare @ns int
select @ns = count(*) from #filtreSeances
if (@ns =0)
BEGIN
	insert into #filtreSeances 
	select seance_id from seance where manifestation_id = [eventID]
end

SELECT type_tarif_id, categ_id, seance_id, 
convert(int, AmountExceptTax * 100) as AmountExceptTax, 
convert(int,Charge * 100) as Charge , 
convert(int,Tax * 100) as Tax, 
convert(int,Discount * 100) as Discount, 
convert(int,TotalTax * 100) as TotalTax, 
convert(int,TotalAmount *100) as TotalAmount,
convert(int,TicketAmount *100) as TicketAmount,
convert(int, vts_id) as vts_id
FROM (
	SELECT 
	type_tarif_id,
	categ_id,
	convert(int,vts.seance_id) as seance_id,
	vts.vts_grille1 as AmountExceptTax,
	vts.vts_grille2 as Charge,
	vts.vts_grille3 as TicketAmount,
	Tax= case when modecol4='TAXE' then vts.vts_grille4 else 0 END + case  when modecol5='TAXE' then vts.vts_grille5 else 0 END + case  when modecol6='TAXE' then vts.vts_grille6 else 0 END + case  when modecol7='TAXE' then vts.vts_grille7 else 0 END + case  when modecol8='TAXE' then vts.vts_grille8 else 0 END + case  when modecol9='TAXE' then vts.vts_grille9 else 0 END + case  when modecol10='TAXE' then vts.vts_grille10 else 0 END,
	Discount= case when modecol4='REMISE' then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then vts.vts_grille10 else 0 END,
	Commission= case when modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  when modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='COMMISSION' then vts.vts_grille10 else 0 END,
	TotalTax= case  when modecol4='REMISE'  then - vts.vts_grille4 when modecol4='TAXE' or modecol4='COMMISSION' then  vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then  vts.vts_grille10 else 0 END ,
	TotalAmount= vts.vts_grille1+vts.vts_grille2+case when modecol4='REMISE' then - vts.vts_grille4  when modecol4='TAXE'  then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 
	when modecol5='TAXE' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' then vts.vts_grille6 else 0 END +
	case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE'
	then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' then vts.vts_grille10 else 0 END,
	vts_id 

	FROM valeur_tarif_stock[eventID] vts
	INNER JOIN #filtreSeances s on s.seance_id = vts.seance_id
	INNER JOIN structure st ON st.structure_id >0
	WHERE vts_v = (
			SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2 WHERE vts2.tarif_logique_id=vts.tarif_logique_id
			and vts2.seance_id=vts.seance_id
			and vts2.categ_id= vts.categ_id
			and vts2.type_tarif_id= vts.type_tarif_id
		)
	AND vts.vts_grille1>=0 
) s
drop table #filtreSeances
