﻿using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Cache.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Widgets;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class IndivTunnelController : Controller
    {
        private static readonly RodrigueNLogger Logger = new();

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheSHelper _cacheHelper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IGestionTraceManager _gestionTrace;

        private readonly IPartnerManager _partnerManager;




        public IndivTunnelController(

            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            IMemoryCache memoryCache,
            ICacheSHelper cachHelper,
            IPartnerManager partnerManager
            )
        {
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _memoryCache = memoryCache;

            _cacheHelper = cachHelper;

            _partnerManager = partnerManager;

            //_feedbookTarifsManager = feedbookTarifManager;

        }

        [HttpGet]
        [HttpPost]
        [Route("itunnel/")]
        public IActionResult iTunnel(int structureId, string langCode, int eventId, int identityId, 
            int webUserId, int buyerProfilId, int forceSession, string forceDate, 
            string partnerName, string htmlSelector, 
            string mySettings ="")
        {
            string messageLog = $"itunnel ({structureId}, {langCode}, {eventId}, {forceSession}, {forceDate}, {identityId}, {webUserId}, {buyerProfilId}, {partnerName}, {htmlSelector}, {mySettings}, {RodrigueHttpContext.DomainReferrerUrl})...";

            Logger.Info(structureId, messageLog);

            try
            {
                PartnerDTO? partner = _partnerManager.GetPartnerInfosByName(partnerName);

                if (partner == null)
                    return BadRequest("Partner doesn't exist");

                string widgetSignature = HttpContext.Request.Headers["Signature"]!;

                if (widgetSignature == null)
                    return BadRequest("signature is missing");

                string partnerToken = WidgetSecurityHelper.GeneratePartnerToken(structureId, partner.PartnerName, partner.SecretKey);

                List<string> lstParamsToHash = new()
                {
                    structureId.ToString(),
                    langCode,
                    partnerName,
                    partnerToken,
                    widgetSignature
                };

                string cacheKeyForSettings = _cacheHelper.SetCache(mySettings, 400);

                string hash = WidgetSecurityHelper.GenerateHash(partner.SecretKey, lstParamsToHash);
                string queryHash = WidgetSecurityHelper.GenerateQueryHash(widgetSignature, partnerToken, partnerName);

                string action = RouteData.Values["action"]?.ToString()!;
                string callTunnelHash = $"{action}Hash/{structureId}/{langCode}/{eventId}/{identityId}/{webUserId}/{buyerProfilId}/{forceSession}/{forceDate}/?htmlSelector={htmlSelector}&hash={hash}&queryHash={queryHash}&mySettingsCacheName={cacheKeyForSettings}";

                Logger.Info(structureId, $"tunnel ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate}, {partnerName}, {htmlSelector}) ok");
                Logger.Trace(structureId, $"tunnel ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate}, {partnerName}, {htmlSelector}) ok : {callTunnelHash}");

                return Json(new { responseText = callTunnelHash });
            }
            catch
            {
                throw;
            }

        }

        /// <summary>
        /// methode principale "fchoixseance"
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="htmlSelector"></param>
        /// <param name="hash"></param>
        /// <param name="queryHash"></param>
        /// <returns></returns>
        [Route("iTunnelHash/{structureId}/{langCode}/{eventId}/{identityId}/{webUserId}/{buyerProfilId}/{forceSession}/{forceDate}")]
        public async Task<IActionResult> iTunnelHash(int structureId, string langCode, int eventId, int identityId, int webUserId, int buyerProfilId, int forceSession, string forceDate, string htmlSelector, string hash,
            string queryHash,
            string mySettingsCacheName)
        {
            string typeRun = _configuration["TypeRun"]!;
            SecurityInfos securityData = WidgetSecurityHelper.GetSecurityInfos(queryHash);

            PartnerDTO? partner = _partnerManager.GetPartnerInfosByName(securityData.PartnerName);

            if (partner is null)
                return BadRequest(new { messagr = "Partner doesn't exist" });

            string mySecretKey = partner.SecretKey;

            string toHash = $"{RodrigueHttpContext.SousWidgetsUrl}${Request.Method}";

            string newSignatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, mySecretKey);
           // newSignatureCalculee = HttpUtility.UrlEncode(newSignatureCalculee);

            Logger.Debug(structureId, $"tunnel hash ({structureId}, {langCode}, {eventId}, {identityId}, {webUserId}, {buyerProfilId}, {forceSession}, {forceDate},, {htmlSelector}) ok");
            Logger.Debug(structureId, $"tunnel hash toHash = {toHash} | {mySecretKey.Substring(0,3)}... => newSignature = {newSignatureCalculee}");


            //ViewBag.mySettings = JsonConvert.DeserializeObject<dynamic>(mySettings ?? "");
            string mySettings ="";
            if (mySettingsCacheName != "notSet")
            {
                _memoryCache.TryGetValue(mySettingsCacheName, out mySettings);                                           
            }

            ViewBag.PartnerName = partner.PartnerName;
            ViewBag.HtmlSelector = htmlSelector;

            ViewBag.SignatureWidgetGet = newSignatureCalculee;

            ViewBag.WCatalogUrl = _configuration["WidgetCatalogUrl"]!;
            ViewBag.WOfferUrl = _configuration["WidgetOfferUrl"]!;
            ViewBag.WCustomerUrl = _configuration["WidgetCustomerUrl"]!;
            ViewBag.ApplicationPath = HttpContext.Request.PathBase;

            ViewBag.StructureId = structureId;
            ViewBag.EventId = eventId;
            ViewBag.ForceSession = forceSession;

            ViewBag.IdentityId = identityId;
            ViewBag.WebUserId = webUserId;
            ViewBag.BuyerProfilId = buyerProfilId;
            ViewBag.mySettings = HttpUtility.UrlDecode(mySettings);

            ViewBag.PartnerToken = securityData.RequestPartnerToken;

            ViewBag.LangCode = langCode;
            ViewBag.DeviseCode = WidgetUtilitiesHelper.GetDeviseCode(structureId);

            return View("Index");
        }
    }
}
