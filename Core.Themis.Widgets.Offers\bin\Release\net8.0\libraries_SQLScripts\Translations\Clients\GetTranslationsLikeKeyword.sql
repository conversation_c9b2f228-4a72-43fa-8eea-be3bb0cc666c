/* \\**************\webservices\dev\libraries\LIBRARIES_SQLSCRIPTS\Translations\Clients\\GetTranslationLikeKeyword.sql */ 
 /*
DECLARE @pKeyword varchar(300)

set @pKeyword = 'Widget_HomeModular_%'
*/

IF (not EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE  TABLE_NAME = 'translate_fieldsSpecificTranslation'))
BEGIN

CREATE TABLE [dbo].[translate_fieldsSpecificTranslation](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[fieldCode] [varchar](max) NULL,
	[val] [varchar](200) NULL,
	[lang_id] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]


ALTER TABLE [dbo].[translate_fieldsSpecificTranslation]  WITH CHECK ADD  CONSTRAINT [FK_translate_fieldsSpecificLocalTranslation_langue] FOREIGN KEY([lang_id])
REFERENCES [dbo].[langue] ([langue_id])



END


	
	IF (EXISTS (SELECT * 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE  TABLE_NAME = 'translate_fieldsSpecificTranslation'))
    BEGIN
        SELECT *
        FROM translate_fieldsSpecificTranslation tfst
        JOIN langue l ON l.langue_id = tfst.lang_id
        WHERE l.supprimer = 'N' and fieldCode like @pKeyword  
        order by fieldCode 
    END

