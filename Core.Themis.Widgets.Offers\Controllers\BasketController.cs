﻿using Core.Themis.Libraries.BLL.Extentions;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Consumer.Interfaces;
using Core.Themis.Libraries.BLL.Products.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.WT;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.BLL.InfoComp.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.InfoComps.Json;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.Razor.Areas.CustomerArea.ViewModels;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Helpers.Cache.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Files;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Widgets;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.Models;
using Core.Themis.Widgets.Offers.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.IISIntegration;
using Microsoft.CodeAnalysis;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;

namespace Core.Themis.Widgets.Offers.Controllers
{
    public class BasketController : Controller
    {
        private static readonly RodrigueNLogger Logger = new();
        private readonly string[]? _areas;

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheSHelper _cacheHelper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IBasketManager _basketManager;
        private readonly IProductManager _productManager;
        private readonly IPartnerManager _partnerManager;
        private readonly ITranslateManager _translateManager;
        private readonly IGestionTraceManager _gestionTrace;
        private readonly IGestionPlaceManager _gestionPlaceManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IConsumerManager _consumerManager;
        private readonly IInfoCompManager _infoCompManager;

        public BasketController(
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            IBasketManager basketManager,
            IProductManager productManager,
            IMemoryCache memoryCache,
            ICacheSHelper cachHelper,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IPartnerManager partnerManager,
            ITranslateManager translateManager,
            IGestionTraceManager gestionTrace,
            IGestionPlaceManager gestionPlaceManager,
            IBuyerProfilManager buyerProfilManager,
            IConsumerManager consumerManager,
            IInfoCompManager infoCompManager)
        {
            _areas = configuration.GetSection("TranslationsAreas:Basket").Get<string[]>();
            _httpContextAccessor = httpContextAccessor;
            _memoryCache = memoryCache;
            _cacheHelper = cachHelper;
            _configuration = configuration;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _basketManager = basketManager;
            _productManager = productManager;
            _partnerManager = partnerManager;
            _translateManager = translateManager;
            _gestionTrace = gestionTrace;
            _gestionPlaceManager = gestionPlaceManager;
            _buyerProfilManager = buyerProfilManager;
            _consumerManager = consumerManager;
            _infoCompManager = infoCompManager;

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="basketId"></param>
        /// <param name="lastEventId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="partnerName"></param>
        /// <param name="canal">useContext de vente : vindiv : vente "normale" depuis themisweb / tunnel : vente via un tunnel d'achat depuis site externe</param>
        /// <param name="htmlSelector"></param>
        /// <returns></returns>
        [Route("Basket/")]
        public IActionResult Basket(int structureId, string langCode, int basketId, int lastEventId, int identityId, int webUserId,
            int buyerProfilId, string partnerName, string useContext, string htmlSelector, string mySettings = "")
        {
            Logger.Info(structureId, $"Basket ({structureId}, {langCode}, {basketId}, {lastEventId}, {identityId}, {webUserId}, {buyerProfilId}, {partnerName}, {useContext}, {htmlSelector})...");

            try
            {
                PartnerDTO? partner = _partnerManager.GetPartnerInfosByName(partnerName);

                if (partner == null)
                    return BadRequest("Partner doesn't exist");

                string? widgetSignature = Request.Headers["Signature"];

                if (widgetSignature == null)
                    return BadRequest("signature is missing");




                string partnerToken = WidgetSecurityHelper.GeneratePartnerToken(structureId, partner.PartnerName, partner.SecretKey);

                List<string> lstParamsToHash = new()
                {
                    structureId.ToString(),
                    langCode,
                    partnerName,
                    partnerToken,
                    HttpUtility.HtmlDecode(widgetSignature)
                };

                string hash = WidgetSecurityHelper.GenerateHash(partner.SecretKey, lstParamsToHash);
                string queryHash = WidgetSecurityHelper.GenerateQueryHash(HttpUtility.HtmlDecode(widgetSignature), partnerToken, partnerName);

                string action = RouteData.Values["action"]?.ToString()!;

                string cacheKeyForSettings = _cacheHelper.SetCache(mySettings, 400);

                string callBasketHash = $"{action}Hash/{structureId}/{langCode}/{basketId}/{lastEventId}/{identityId}/{webUserId}/{buyerProfilId}/?useContext={useContext}&htmlSelector={htmlSelector}&hash={hash}&queryHash={queryHash}&mySettingsCacheName={cacheKeyForSettings}";

                Logger.Info(structureId, $"Basket ({structureId}, {langCode}, {basketId}, {lastEventId}, {identityId}, {webUserId}, {buyerProfilId}, {partnerName}, {useContext}, {htmlSelector}) ok");
                Logger.Trace(structureId, $"Basket ({structureId}, {langCode}, {basketId}, {lastEventId}, {identityId}, {webUserId}, {buyerProfilId}, {partnerName},  {useContext}, {htmlSelector}) ok : {callBasketHash}");

                return Json(new { responseText = callBasketHash });
            }
            catch
            {
                throw;
            }
        }

        [Route("BasketHash/{structureId}/{langCode}/{basketId}/{lasteventId}/{identityId}/{webUserId}/{buyerProfilId}")]
        public async Task<IActionResult> BasketHash(int structureId, string langCode, int basketId, int lasteventId, int identityId, int webUserId, int buyerProfilId,
            string useContext, string htmlSelector, string hash, string queryHash,
            string mySettingsCacheName)
        {
            Logger.Info(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId}, " +
                $"{buyerProfilId}, {hash}, {queryHash})...");

            string cryptoKey = _configuration["CryptoKey"].ToString();

            string decodeQueryHash = queryHash.Base64Decode();
            string decodeHashEncode = hash.Base64Decode();

            #region Récupére token et widgetSignature (décryptage de queryHash)

            IDictionary<string, string> dictQueryParam = new Dictionary<string, string>();
            NameValueCollection decryptedStringqueryHash = ThemisQueryStringHandler.DecryptQueryStrings(decodeQueryHash, cryptoKey);
            foreach (string querystring in decryptedStringqueryHash)
            {
                dictQueryParam.Add(querystring, decryptedStringqueryHash[querystring]);

                Logger.Trace(structureId, $"querystring query hash {querystring} => {decryptedStringqueryHash[querystring]}");
            }

            // dictQueryParam contient partnerToken et widgetSignature 
            string requestPartnerToken = "";
            if (dictQueryParam.ContainsKey("partnerToken"))
            {
                requestPartnerToken = dictQueryParam["partnerToken"];
            }

            string requestWidgetSignature = "";
            if (dictQueryParam.ContainsKey("widgetSignature"))
            {
                requestWidgetSignature = dictQueryParam["widgetSignature"];
            }
            #endregion

            string myPathBaseSansSlash = _httpContextAccessor.HttpContext.Request.PathBase;
            if (myPathBaseSansSlash.EndsWith("/"))
                myPathBaseSansSlash = myPathBaseSansSlash.Substring(0, myPathBaseSansSlash.Length - 1);

            string completeUrl = _httpContextAccessor.HttpContext.Request.Scheme + "://" +
                _httpContextAccessor.HttpContext.Request.Host + myPathBaseSansSlash + Request.Path + Request.QueryString;

            Logger.Trace(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId},  h, qh) scheme = {_httpContextAccessor.HttpContext.Request.Scheme}");
            Logger.Trace(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId},  h, qh) host = {_httpContextAccessor.HttpContext.Request.Host}");
            Logger.Trace(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId},  h, qh) pathBase = {myPathBaseSansSlash}");
            Logger.Trace(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId},  h, qh) path= {Request.Path}");
            Logger.Trace(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId},  h, qh) querystring = {Request.QueryString}");

            string toHash = completeUrl + "$" + "GET";

            Logger.Info(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId}, {buyerProfilId}, h, qh) toHash = {toHash}");


            int partnerId = TokenManager.getPartnerIdFromToken(requestPartnerToken);
            Logger.Info(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId}, {buyerProfilId}, {hash}, {queryHash}) partnerId = {partnerId}");

            if (partnerId != 0)
            {
                PartnerDTO partner = _partnerManager.GetPartnerInfosById(partnerId)!;

                string mySecretKey = partner.SecretKey;

                string signatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, mySecretKey);

                Logger.Info(structureId, $"BasketHash ({structureId}, {langCode}, {basketId}, {lasteventId}, {identityId}, {webUserId}, {buyerProfilId}, {hash}, {queryHash}) signatureCalculee GET = {signatureCalculee}");

                string configIniPath = _configuration["ConfigIniPath"].ToString();
                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                string urlBasePaiement = _configuration["SitePaiement"]; // System.Configuration.ConfigurationManager.AppSettings["SitePaiement"];

                int filierId = 0;
                if (configIniXml.ContainsKey("CREATEPROFILWEBFILIEREID") && configIniXml["CREATEPROFILWEBFILIEREID"] != null && configIniXml["CREATEPROFILWEBFILIEREID"] != "")
                {
                    int.TryParse(configIniXml["CREATEPROFILWEBFILIEREID"], out filierId);
                }

                // get Grille Tarif , big one
                // call api offers grille tarif
                if (!_basketManager.CheckPriceAdhesions(structureId, basketId))
                {
                    var lstWrongBasketLines = _basketManager.GetWrongBasketLinesForAdhesion(structureId, basketId);
                    if (_basketManager.DeleteWrongBasketLineForAdhesion(structureId, basketId))
                    {
                        ViewBag.WrongBasketLineForAdhesion = lstWrongBasketLines;
                    }

                    string errorMessage = $"Un tarif type Carte Avantage ne se trouve pas dans la table sponsor_panier_entrees";
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, errorMessage);
                    Logger.Error(structureId, errorMessage);
                }

                BasketDTO bask2 = _basketManager.BasketTransformator(structureId, basketId, langCode);

                _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Call BasketTransformator:{basketId}");

                List<ProductDTO> listRet = new();
                listRet = _productManager.LoadFraisDossier(structureId, filierId, langCode);
                _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Call LoadFraisDossier filiere:{filierId} panier {basketId}");

                if (listRet != null && listRet.Count > 0)
                {
                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Call DelFraisDossier panier:{basketId}");
                    bool ret = _basketManager.DelFraisDossier(structureId, basketId);

                    if (!ret)
                        _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"DelFraisDossier retourne {ret} sur le panier {basketId}");


                    _gestionTrace.WriteLogGeneriqueMessage(structureId, webUserId, $"Call AddFraisDossier  panier:{basketId} products:{string.Join(",", listRet.Select(p => p.ProductId))}");
                    bool ret2 = _basketManager.AddFraisDossier(structureId, basketId, listRet);
                    if (!ret2)
                        _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"AddFraisDossier retourne {ret2} sur le panier {basketId}");
                }

                //List<ProductDTO> r = await ApiOffersHelper.AddFraisDossier(apiOfferUrl, requestPartnerToken, partner.SecretKey, structureId, basketId, filierId, langCode);
                // BasketDTO bask = await ApiOffersHelper.GetWithOpen(apiOfferUrl, requestPartnerToken, structureId, basketId, langCode);

                BasketDTO bask = _basketManager.GetAllBasketInfo(structureId, basketId, 0).FirstOrDefault();
                _basketManager.FillFromOpen(structureId, bask, langCode);



                // get mode d'obtentions disponibles :
                //List<int> uniqRuleId = (from seat in bask.ListAllSeats() group seat by new { seat.RuleId } into mygroup select mygroup.FirstOrDefault().RuleId).ToList();


                List<int> uniqRuleId = bask.ListAllSeats().Select(b => b.Price).Select(p => p.RuleId).ToList();


                List<int> uniqProdWTId = (from prodWT in bask.ListProduitsWT
                                          where prodWT.TypeInsertion != "AUTO"
                                          group prodWT by new { prodWT.ProductId } into mygroup
                                          select mygroup.FirstOrDefault().ProductId).ToList();

                List<int> uniqProdAutoWTId = (from prodWT in bask.ListProduitsWT
                                              where prodWT.TypeInsertion == "AUTO"
                                              group prodWT by new { prodWT.ProductId } into mygroup
                                              select mygroup.FirstOrDefault().ProductId).ToList();

                List<int> uniqProdCarAdhId = (from prodCAdh in bask.ListProductCartesAdhesion group prodCAdh by new { prodCAdh.ProductId } into mygroup select mygroup.FirstOrDefault().ProductId).ToList();

                if (uniqRuleId.Count + uniqProdWTId.Count + uniqProdCarAdhId.Count == 0)
                {
                    Logger.Error(structureId, $"BasketHash - neither seats nor products");
                    _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"BasketHash - neither seats nor products {basketId}");

                    //var error = new ErrorViewModel
                    //{
                    //    IpAddress = RodrigueHttpContext.IpAddress,
                    //    Code = (int)HttpStatusCode.NotFound,
                    //    Message = "",
                    //    Date = DateTime.Now,
                    //    HtmlSelector = htmlSelector
                    //};
                    //return View("Error", error);
                    return View("BasketIsEmpty");
                }
                else
                {
                    List<ProductDTO> productsMOForSeats = new List<ProductDTO>();
                    List<ProductDTO> productsMOProducts = new List<ProductDTO>();

                    dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, buyerProfilId, "", langCode, "physicalPathOfSettingsJSON");

                    if (mySettingsCacheName != "notSet")
                    {
                        //appsettings
                        string mySettings;
                        if (_memoryCache.TryGetValue(mySettingsCacheName, out mySettings))
                        {
                            try
                            {
                                settingsMerged = WidgetUtilitiesHelper.MergeSettings(settingsMerged, JsonConvert.DeserializeObject<dynamic>(mySettings ?? ""));
                            }
                            catch (Exception ex) // client envoie un truc mal formaté, passer outre
                            {
                                Logger.Error(structureId, $"error {mySettings} : {ex.Message} {ex.StackTrace}");
                            }
                        }

                    }

                    //info comps

                    if (bask.IdentityId > 0)
                    {

                        NameValueCollection nameValueCollection = new()
                            {
                                {"structureId", structureId.ToString()},
                                {"basketId", bask.BasketId.ToString()},
                                {"identityId", bask.IdentityId.ToString()}
                            };

                        ViewBag.infosCompHash = ThemisQueryStringHandler.EncryptQueryStrings(nameValueCollection, cryptoKey);

                        var infoCompGroupSettings = _infoCompManager.LoadJsonToObject(structureId);
                        ViewBag.infosCompJson = await _infoCompManager.PopulateAssignedInfoCompsAsync(structureId, bask.IdentityId, infoCompGroupSettings).ConfigureAwait(false);
                    }
                    else
                    {
                        ViewBag.infosCompJson = null;
                    }


                    //Test les chemin du fichier CGV et retourne l'adresse url
                    ViewBag.CGVurl = WidgetUtilitiesHelper.GetCGVUrl(structureId, langCode, settingsMerged);

                    ViewBag.SettingsMerge = (settingsMerged != null) ? settingsMerged : "";

                    #region les produits à la séance / manif / global au panier

                    #region produits globaux au panier
                    //List<ProductFamilyDTO> prodsGlobToCache = await ApiOffersHelper.GetListProductsGlobalAuPanier(apiOfferUrl, requestPartnerToken, structureId, langCode);
                    List<ProductFamilyDTO> productsGlobaux = _productManager.LoadProductsGlobauxBasket(structureId, langCode);



                    #endregion

                    #region produit d'assurance
                    /* ProductEntity prodInsurance = new ProductEntity();

                     string cachNameInsuranceProduct = $"GetInsuranceProduct[{structureId}.{basketId}.{langCode}]";

                     if (!_memoryCache.TryGetValue(cachNameInsuranceProduct, out prodInsurance))// Look for cache key.
                     {
                         prodInsurance = await InsuranceModel.GetInsuranceProduct(apiOfferUrl, requestPartnerToken, structureId, basketId, langCode);

                         int durationCache = 600;
                         if (_configuration["Cache:InsuranceAbsoluteExpiration"] != null)
                         {
                             durationCache = int.Parse(_configuration["Cache:InsuranceAbsoluteExpiration"].ToString());
                             Logger.Trace(structureId, $"GetInsurance - durée du cache {durationCache} secondes");
                         }

                         //mise en cache pendant 10 minutes ou pendant la durée définie dans le fichier apsettings.json
                         var cacheEntryOptions = new MemoryCacheEntryOptions()
                            .SetPriority(CacheItemPriority.Normal)
                            .SetAbsoluteExpiration(TimeSpan.FromSeconds(durationCache));
                         _memoryCache.Set(cachNameInsuranceProduct, prodInsurance, cacheEntryOptions);


                     }
                    */

                    #endregion

                    #region produits à la manifs
                    List<int> uniqEventId = (from seat in bask.ListAllSeats() group seat by new { seat.EventId } into mygroup select mygroup.FirstOrDefault().EventId).ToList();
                    List<EventDTO> eventEntities = _productManager.LoadProductsIntoEvents(structureId, langCode, uniqEventId);
                    #endregion

                    #region produits à la séance
                    List<int> uniqSeanceId = (from seat in bask.ListAllSeats() group seat by new { seat.SessionId } into mygroup select mygroup.FirstOrDefault().SessionId).ToList();
                    List<SessionDTO> sessionEntities = _productManager.LoadProductsIntoSessions(structureId, langCode, uniqSeanceId);
                    #endregion

                    if (bask.ContainsProduitsPurs)
                    {
                        // repositionner le count des produits globaux au bon endroit 
                        foreach (var prodinbask in bask.ListProduitsWT.Where(p => p.EventId == 0))
                        {
                            int thisProdId = prodinbask.ProductId;
                            int myCountInBasket = prodinbask.Count;

                            if (productsGlobaux.Count > 0)
                            {
                                var thisProdInGlobal = productsGlobaux[0].SubFamiliesList[0].ProductsList.Where(p => p.ProductId == thisProdId).FirstOrDefault();
                                if (thisProdInGlobal != null)
                                {
                                    int countInBask = prodinbask.Count;

                                    thisProdInGlobal.TypeInsertion = prodinbask.TypeInsertion;
                                    thisProdInGlobal.TypeLigne = prodinbask.TypeLigne;

                                    if (thisProdInGlobal.AmountIsVariable)
                                    {
                                        int amount = prodinbask.AmountTTCCents;
                                        thisProdInGlobal.Count = 1;
                                        thisProdInGlobal.AmountTTCCents = amount;
                                    }
                                    else if (thisProdInGlobal.TypeMontant == 2)
                                    {

                                        var resultMontant = _basketManager.GetCalculProductByStoredProcedure(structureId, basketId, identityId, prodinbask.ProductId);
                                        if (resultMontant is not null)
                                        {
                                            thisProdInGlobal.Count = 1;
                                            thisProdInGlobal.AmountTTCCents = (int)resultMontant;
                                        }
                                    }
                                    else
                                    {
                                        thisProdInGlobal.Count = myCountInBasket;
                                    }

                                    prodinbask.Count = 0;

                                }
                            }

                        }

                        // repositionner le count des produits manif et séance au bon endroit 
                        foreach (var prodinbask in bask.ListProduitsWT.Where(p => p.EventId > 0))
                        {
                            if (eventEntities != null)
                            {
                                int myCountInBasket = prodinbask.Count;
                                int thisEvent = prodinbask.EventId;
                                int thisSessionId = prodinbask.SessionId;
                                var thisE = eventEntities.Where(p => p.EventId == thisEvent).FirstOrDefault();
                                if (thisE != null)
                                {
                                    if (thisE.ProductsBoutique.ProductFamiliesList.Count > 0)
                                    {

                                        var thisProdInEvent = thisE.ProductsBoutique.ProductFamiliesList[0].
                                             SubFamiliesList[0].ProductsList.Where(p => p.ProductId == prodinbask.ProductId).FirstOrDefault();
                                        if (thisProdInEvent != null)
                                        {

                                            thisProdInEvent.TypeInsertion = prodinbask.TypeInsertion;
                                            thisProdInEvent.TypeLigne = prodinbask.TypeLigne;

                                            if (thisProdInEvent.AmountIsVariable)
                                            {
                                                thisProdInEvent.Count = 1;
                                                thisProdInEvent.AmountTTCCents = prodinbask.AmountTTCCents;
                                            }
                                            else
                                            {
                                                thisProdInEvent.Count = myCountInBasket;
                                            }
                                        }
                                    }

                                }
                                var thisS = sessionEntities.Where(p => p.SessionId == thisSessionId).FirstOrDefault();
                                if (thisS != null)
                                {
                                    if (thisS.ProductsBoutique.ProductFamiliesList.Count > 0)
                                    {
                                        var thisProdInSession = thisS.ProductsBoutique.ProductFamiliesList[0].
                                      SubFamiliesList[0].ProductsList.Where(p => p.ProductId == prodinbask.ProductId).FirstOrDefault();
                                        if (thisProdInSession != null)
                                        {

                                            thisProdInSession.TypeInsertion = prodinbask.TypeInsertion;
                                            thisProdInSession.TypeLigne = prodinbask.TypeLigne;

                                            if (thisProdInSession.AmountIsVariable)
                                            {
                                                thisProdInSession.Count = 1;
                                                thisProdInSession.AmountTTCCents = prodinbask.AmountTTCCents;
                                            }
                                            else
                                            {
                                                thisProdInSession.Count = myCountInBasket;
                                            }


                                        }
                                    }
                                }
                            }
                        }
                    }

                    ViewBag.prodsByEvents = eventEntities;
                    ViewBag.prodsBySessions = sessionEntities;
                    ViewBag.prodsGlob = productsGlobaux;
                    // ViewBag.prodInsurance = prodInsurance;
                    #endregion

                    #region les modes d'obtentions 

                    var modelToUseInPartial = await _productManager.getListMOObject(structureId, langCode, basketId, buyerProfilId,
                        uniqRuleId, uniqProdWTId, uniqProdCarAdhId, identityId, bask
                        );


                    ViewBag.initialmodelToUseInPartial = modelToUseInPartial;

                    #endregion

                    BuyerProfilDTO thisBP = new();
                    if (buyerProfilId != 0)
                    {
                        thisBP = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId);

                        // thisBP = await ApiOffersHelper.GetViaExterne(apiOfferUrl, requestPartnerToken, structureId, buyerProfilId);
                    }

                    ViewBag.Basket = bask;
                    var translationsList = _translateManager.GetTranslationsByLangCode(structureId, langCode, _areas);
                    ViewBag.TranslationsList = translationsList;

                    ViewBag.BuyerProfil = thisBP;
                    ViewBag.DeviseCode = WidgetUtilitiesHelper.GetDeviseCode(structureId);
                    ViewBag.HtmlSelector = htmlSelector;
                    ViewBag.SignatureWidgetGet = signatureCalculee;
                    ViewBag.WCatalogUrl = _configuration["WidgetCatalogUrl"]!;
                    ViewBag.WOfferUrl = _configuration["WidgetOfferUrl"]!;
                    ViewBag.ApplicationPath = _httpContextAccessor.HttpContext.Request.PathBase;
                    ViewBag.StructureId = structureId;
                    ViewBag.EventId = lasteventId;
                    ViewBag.IdentityId = identityId;
                    ViewBag.WebUserId = webUserId;
                    ViewBag.BuyerProfilId = buyerProfilId;
                    ViewBag.PartnerToken = requestPartnerToken;
                    ViewBag.LangCode = langCode;
                    ViewBag.CustomPackageUrl = WidgetUtilitiesHelper.GetUrlCustomPackage(structureId);

                    CustomerAreaAppSettings appSettings = new()
                    {
                        AppStartingRoute = $"./login",
                        StructureId = structureId,
                        IdentiteId = identityId,
                        WebUserId = webUserId,
                        BasketId = basketId,
                        LangCode = langCode,
                        UseContext = useContext,
                        PartnerName = partner.PartnerName,
                        HtmlSelector = htmlSelector

                    };

                    return View("Index", appSettings);
                }
            }

            var errorvm = new ErrorViewModel
            {
                IpAddress = RodrigueHttpContext.IpAddress,
                Code = (int)HttpStatusCode.NotFound,
                Message = "BasketHash",
                Date = DateTime.Now,
                HtmlSelector = ""
            };
            return View("Error", errorvm);

        }

        /// <summary>
        /// insert consommateur de profil d'acheteur
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="ConsommName"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [Route("{structureId}/insertConsumerBPAjax/{basketId}")]
        [HttpPost]
        public async Task<IActionResult> InsertConsumerBPAjax(int structureId, int basketId, string ConsommName, string token)
        {
            int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

            if (!partnerIdToken.HasValue)
            {
                Logger.Error(structureId, $"Impossible de récupérer le token dans InsertConsumerBPAjax basketid {basketId} pour la structure {structureId}");
                return Problem("Invalid token");
            }
            else
            {
                // insertion dans la table Open consumers
                int consumerId = _consumerManager.CreateConsumer(structureId, ConsommName.ToString());
                Logger.Debug(structureId, $"consommateur créé : {consumerId}");


                bool isUpdated = _basketManager.UpdateConsumerPanierEntree(structureId, basketId, consumerId);
                Logger.Debug(structureId, $"panier mis à jour sur le consommateur : {consumerId} pour le panier {basketId}");

                return Ok(isUpdated);
            }
        }

        #region Infocomps

        /// <returns></returns>
        [Route("{structureId}/insertInfoCompsForIdentiteAjax/{basketId}/{identityId}")]
        [HttpPost]
        public async Task<IActionResult> InsertInfocompsForIdentiteAjax(int structureId, int basketId, int identityId, JsonInfoCompGroupSettings infoCompGroupSettings, string token, string infosCompHash)
        {
            int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);
            
            try
            {
                if (!partnerIdToken.HasValue)
                {
                    Logger.Error(structureId, $"Impossible de récupérer le token dans InsertInfocompsForIdentiteAjax basketId {basketId}, identityId {identityId} pour la structure {structureId}");
                    return Problem("Invalid token");
                }
                else
                {
                    string cryptoKey = _configuration["CryptoKey"].ToString();

                    NameValueCollection nameValueCollection = new()
                            {
                                {"structureId", structureId.ToString()},
                                {"basketId", basketId.ToString()},
                                {"identityId", identityId.ToString()}
                            };

                    // Déchiffrez celle venue dans le token
                    NameValueCollection nameValueCollectionDecrypted = ThemisQueryStringHandler.DecryptQueryStrings(HttpUtility.HtmlDecode(infosCompHash), cryptoKey);

                    // Recréation du hash à partir de la collection montée
                    var computedHash = ThemisQueryStringHandler.EncryptQueryStrings(nameValueCollection, cryptoKey);

                    // Check si les clé et valeurs sont identiques
                    if (!nameValueCollection.SequenceEqualTo(nameValueCollectionDecrypted))
                    {
                        Logger.Error(structureId, $"Hash différent pour l'identité {identityId}: computed=[{computedHash}], received=[{infosCompHash}]");
                        throw new InvalidOperationException($"Le hash ne correspond pas aux paramètres attendus. (computed={computedHash}, received={infosCompHash})");
                    }

                    BasketDTO bask = _basketManager.GetAllBasketInfo(structureId, basketId, 0).FirstOrDefault();

                    if (bask.IdentityId > 0)
                    {
                        await _infoCompManager.UpdateIdentityInfoCompAssignmentsAsync(structureId, bask.IdentityId, infoCompGroupSettings).ConfigureAwait(false);

                    }
                }

                return Ok(new { success = true });

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"InsertInfocompsForIdentiteAjax failed (structure={structureId}, basketId={basketId})");
                return StatusCode(StatusCodes.Status500InternalServerError, "Server error");
            }

        }
        #endregion


        #region modes d'obtentions
        /// <summary>
        /// Insert les mode d'obtentions dans le panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="identiteId"></param>
        /// <param name="obtainingMode">Mode d'obtention avec le type (MO_ENTREES, MO_PRODUITS) et le produit id</param>
        /// <param name="langCode">langue</param>
        /// <returns></returns>
        [Route("{structureId}/insertObtainingModeAjax/{basketId}")]
        [HttpPost]
        public async Task<IActionResult> InsertObtainingModeAjax(int structureId, int basketId, int identiteId,
        Dictionary<string, int> obtainingMode, string langCode, string token)
        {

            int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

            if (!partnerIdToken.HasValue)
            {
                Logger.Error(structureId, $"Impossible de récupérer le token dans InsertObtainingModeAjax basketid {basketId} pour la structure {structureId}");
                return Problem("Invalid token");
            }
            else
            {

                if ((obtainingMode.Keys.Count != 2)
                    || !obtainingMode.Keys.Contains("MO_ENTREES")
                    || !obtainingMode.Keys.Contains("MO_PRODUCTS")
                    )
                {
                    Logger.Error(structureId, $"obtainingMode dico error : {JsonConvert.SerializeObject(obtainingMode, Formatting.Indented)} et la struture {structureId}");
                    // throw error; 
                }

                try
                {
                    var Listbask = _basketManager.GetAllBasketInfo(structureId, basketId, 0);
                    if (Listbask != null)
                    {
                        var bask = Listbask[0];
                        if (bask == null)
                        {
                            Logger.Error(structureId, $"Impossible de récupérer le panier : {bask.BasketId} pour la structure {structureId}");
                            throw new Exception("problème récupération du panier");
                        }

                        //var sessions = bask.ListEventsUnitSales.SelectMany(evt => evt.ListSessions).ToList();


                        //var zones = sessions.SelectMany(ses => ses.ListZones).ToList();

                        //var floors = zones.SelectMany(f => f.ListFloors).ToList();
                        //var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                        //var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                        //var prices = categories.Distinct().ToList().SelectMany(p => p.ListPrices).Distinct().ToList();
                        //var gestionplaces = prices.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Distinct().ToList();

                        // var lstGestionPlaceId = gestionplaces.Select(gp => gp.GestionPlaceId).Distinct().ToList();

                        //List<int> lstGestionPlaceId = (from seat in bask.ListAllSeats() group seat by new { seat.RuleId } into mygroup select mygroup.FirstOrDefault().RuleId).ToList();
                        List<int> lstGestionPlaceId = bask.ListAllSeats().Select(b => b.Price).Select(p => p.RuleId).ToList();

                        int modSeatsId = obtainingMode["MO_ENTREES"];
                        int modProdsId = obtainingMode["MO_PRODUCTS"];

                        List<int> listMOAJustifOsef = new List<int>();
                        List<int> uniqProdWTId = (from prodWT in bask.ListProduitsWT group prodWT by new { prodWT.ProductId } into mygroup select mygroup.FirstOrDefault().ProductId).ToList();
                        List<int> uniqProdCarAdhId = (from prodCAdh in bask.ListProductCartesAdhesion group prodCAdh by new { prodCAdh.ProductId } into mygroup select mygroup.FirstOrDefault().ProductId).ToList();

                        if (lstGestionPlaceId.Count > 0 || uniqProdWTId.Count() + uniqProdCarAdhId.Count() > 0)
                        {
                            // charge les informations du mode d'obtention
                            var deletedObtainingMode = _basketManager.DeleteAllObtentionModes(structureId, basketId);

                            if (lstGestionPlaceId.Count > 0) //////////// des sieges !!!!!!!!!!!!!!!!
                            {
                                //List<ProductDTO> obtentionModeSeats = await ApiOffersHelper.GetListProductsMO(apiOfferUrl, token, structureId, langCode, lstGestionPlaceId, listMOAJustifOsef);
                                List<ProductDTO> obtentionModeSeats = _productManager.GetAllObtainingModeForProducts(structureId, listMOAJustifOsef, langCode, bask, 0, ListGpIds: lstGestionPlaceId);

                                if (obtentionModeSeats.ToList().Count == 0)
                                {
                                    _gestionTrace.WriteLogErrorMessage(structureId, bask.UserId, $"Aucun élément dans la liste productsMO");
                                    Logger.Error(structureId, $"InsertObtainingModeAjax - Aucun élément dans la liste productsMO");

                                    var errorvm = new ErrorViewModel
                                    {
                                        IpAddress = RodrigueHttpContext.IpAddress,
                                        Code = 404,
                                        Message = "InsertObtainingModeAjax",
                                        Date = DateTime.Now,
                                        HtmlSelector = ""
                                    };
                                    return View("Error", errorvm);

                                }

                                // Charge la liste des gestion place type envoi pour avoir la maquette id de chaque mode d'obtention
                                var lstGestionPlaceTypeEnvoi = _gestionPlaceManager.LoadGpTypeEnvoi_byProductId(structureId, lstGestionPlaceId, modSeatsId);

                                ProductDTO thisProdMO = obtentionModeSeats.Where(p => p.ProductId == modSeatsId).FirstOrDefault();

                                if (thisProdMO != null)
                                {
                                    thisProdMO.TypeLigne = "MO";
                                    thisProdMO.Count = 1;

                                    // regroupe la liste des gestion place (mode d'obtention) par maquette
                                    var grpGestionPlaceTypeEnvoi = lstGestionPlaceTypeEnvoi.GroupBy(gpte => gpte.MaquetteBilletId, (key, grp) => new { MaquetteId = key ?? 0, Gestionplaces = grp.ToList() });

                                    // ajoute dans le panier produit le mode d'obtention
                                    int insertResult = _basketManager.InsertPanierProduit(structureId, basketId, thisProdMO);

                                    if (insertResult == 0)
                                    {
                                        Logger.Error(structureId, $"Insertion panier produit n'a pas réussi panier id : {basketId} produit id : {thisProdMO.ProductId}");
                                        throw new Exception("problème Insert panier produit");
                                    }

                                    // pour chaque élément groupé on met à jour le panier_entree (maquette_id) sur la liste des gestion_place_id
                                    foreach (var gpte in grpGestionPlaceTypeEnvoi)
                                    {
                                        _basketManager.UpdateTypeEnvoiAndMaquette(structureId, basketId, gpte.Gestionplaces.Select(gp => gp.GestionPlaceId).ToList(), gpte.MaquetteId, modSeatsId);
                                    }
                                }
                            }

                            /////////// les produits !!!!!!!!!!!!!!
                            if (uniqProdWTId.Count() > 0 || uniqProdCarAdhId.Count() > 0)
                            {
                                if (lstGestionPlaceId.Count == 0
                                    || modProdsId != modSeatsId) // s'il n'y a pas de seats ou que le mode d'obtention est different, on insere le mo des produits 
                                {
                                    //List<ProductDTO> obtentionModeProducts = await ApiOffersHelper.GetListProductsMOProducts(apiOfferUrl, token, structureId,
                                    //    langCode, uniqProdWTId.ToList(), uniqProdCarAdhId.ToList(), listMOAJustifOsef
                                    //);

                                    List<int> listAllProds = new List<int>();
                                    listAllProds.AddRange(uniqProdWTId);
                                    listAllProds.AddRange(uniqProdCarAdhId);

                                    List<ProductDTO> obtentionModeProducts = _productManager.GetAllObtainingModeForProducts(structureId, listMOAJustifOsef, langCode, bask, 0, listProds: listAllProds);

                                    ProductDTO thisProdMO = obtentionModeProducts.Where(p => p.ProductId == modProdsId).FirstOrDefault();
                                    if (thisProdMO != null)
                                    {
                                        thisProdMO.TypeLigne = "MO";
                                        thisProdMO.Count = 1;
                                        int insertResult = _basketManager.InsertPanierProduit(structureId, basketId, thisProdMO);

                                        if (insertResult == 0)
                                        {
                                            Logger.Error(structureId, $"Insertion panier produit n'a pas réussi panier id : {basketId} produit id : {thisProdMO.ProductId}");
                                            throw new Exception("problème Insert panier produit");
                                        }
                                    }
                                    else
                                    {
                                        Logger.Error(structureId, $"thisProdMO=null ! panier id : {basketId} modProdsId: {modProdsId}");
                                        Exception ex = new Exception($"can't retrieve mo {modProdsId} !");
                                        throw ex;

                                    }
                                }

                                /// maj les maquettes, type_envoi_id du panier_produit ?
                                //Dictionary<int, int> dicMaquetteProduct = await ApiOffersHelper.GetProductsMaquette(apiOfferUrl, token, structureId, modProdsId);
                                Dictionary<int, int> dicMaquetteProduct = _productManager.LoadMaquettes(structureId, modProdsId);
                                var grpGestionPlaceTypeEnvoi2 = dicMaquetteProduct.GroupBy(gpte => gpte.Value, (key, grp) => new { maquetteId = key, products = grp.Select(k => k.Key).ToList() });

                                foreach (var maqId in grpGestionPlaceTypeEnvoi2)
                                {
                                    var prodOfMaquettes = maqId.products.ToList();
                                    _basketManager.UpdateTypeEnvoiAndMaquetteProducts(structureId, basketId, prodOfMaquettes, maqId.maquetteId, modProdsId);
                                }
                            }

                            string completeUrl = LinkPaiementAjax(structureId, basketId, identiteId, langCode);
                            return Ok(completeUrl);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"InsertObtainingModeAjax ({structureId}, {basketId}, {langCode}, {identiteId} )");

                    Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    return Problem("Server error");
                }
            }
            // Ne retrouve pas les gestion places dans le panier

            var error = new ErrorViewModel
            {
                IpAddress = RodrigueHttpContext.IpAddress,
                Code = (int)HttpStatusCode.NotFound,
                Message = "can't find gestion_place_id",
                Date = DateTime.Now,
                HtmlSelector = ""
            };
            return View("Error", error);


        }

        /// <summary>
        /// get la partial view mode d'obtention suivant ce que l'internaute a choisi
        /// </summary>
        /// <param name="token"></param>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="basketId"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="listGps"></param>
        /// <param name="listProductsId"></param>
        /// <param name="listProductsCADHId"></param>
        /// <returns></returns>
        [Route("{structureId}/getModesObtentionsViewAjax")]
        [HttpPost]
        public async Task<IActionResult> ModesObtentionsViewAjax(int structureId, string langCode, int basketId, int buyerProfilId,
            int[] listGps, int[] listProductsId, int[] listProductsCADHId, int identiteId)
        {
            BasketDTO basket = new BasketDTO();

            //Récupère tous les produits du panier
            var productIdWithoutInsertionAuto = await _basketManager.GetProductIdsWithoutInsertionAuto(structureId, basketId, listProductsId.ToList()).ConfigureAwait(false);

            var modelToUseInPartial = await _productManager.getListMOObject(structureId, langCode, basketId, buyerProfilId,
                listGps.ToList(), productIdWithoutInsertionAuto, listProductsCADHId.ToList(), identiteId, basket);

            return PartialView("_ModesObtentions", modelToUseInPartial);
        }

        #endregion

        /// <summary>
        /// delete d'un produit 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="webUserId"></param>
        /// <param name="basketProductId"></param>
        /// <param name="langCode"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        [Route("basket/{structureId}/deleteProductAjax/{basketId}/{webUserId}/{langCode}")]
        [HttpDelete]
        public async Task<IActionResult> DeleteProductAjax(int structureId, int basketId, int webUserId,
            int basketProductId, string langCode, string token)
        {
            Logger.Debug(structureId, $"----- start DeleteProductAjax ----- {basketId} {webUserId} {basketProductId} {langCode} {token}");

            int? partnerIdToken = WidgetTokenHelper.ValidateJwtToken(token);

            if (!partnerIdToken.HasValue)
            {
                Logger.Error(structureId, $"Impossible de récupérer le token dans DeleteProductAjax basketid {basketId} pour la structure {structureId}");
                return Problem("Invalid token");
            }
            else
            {
                try
                {
                    Logger.Debug(structureId, $"call DeleteProductsToBasket Manager ----  product: {basketProductId}");
                    var isProductDeleted = _basketManager.DeleteProduct(structureId, basketId, new List<int>() { basketProductId });
                    Logger.Debug(structureId, $"produit supprimé du panier {isProductDeleted}");
                    var newBasket = GetBasket(structureId, basketId, webUserId, langCode);
                    return Ok(newBasket);
                }
                catch (Exception)
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// retourne le lien de paiement pour ce panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        private string LinkPaiementAjax(int structureId, int basketId, int identiteId, string langCode)
        {
            //////////////
            /// 1/ inseer d'obtentions dans panier_produit (type MO) (+ inserer maquette_id sur panier_enrtee)
            /// 2/ retourner le lien de paiement
            ///
            string completeUrl = string.Empty;

            try
            {
                string configIniPath = _configuration["ConfigIniPath"].ToString();
                string cryptoKey = _configuration["CryptoKey"].ToString();

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                string urlBasePaiement = _configuration["SitePaiement"];

                if (configIniXml.ContainsKey("PAIEMENTURLSITE") && configIniXml["PAIEMENTURLSITE"] != null && configIniXml["PAIEMENTURLSITE"] != "")
                {
                    urlBasePaiement = configIniXml["PAIEMENTURLSITE"].ToString();
                }

                Sha1 sha1 = new Sha1("paiementV3_" + structureId + "_" + basketId + "_" + cryptoKey);
                string hash = sha1.getSha1();

                urlBasePaiement = urlBasePaiement.Replace("{structureId}", structureId.ToString("0000"))
                              .Replace("{structureIdShort}", structureId.ToString())
                              .Replace("[structureid]", structureId.ToString())
                              .Replace("[basketid]", basketId.ToString())
                              .Replace("[platformname]", "INDIV")
                              .Replace("[lang]", langCode)
                              .Replace("[haskey]", hash)
                              .Replace("[identiteid]", identiteId.ToString());

                completeUrl = urlBasePaiement;

            }
            catch
            {
                throw;
            }

            return completeUrl;
        }



        [Route("{structureId}/calculProductByStoredProcedureAjax/{basketId}/{productId}/{identityId}")]
        public async Task<IActionResult> CalculProductByStoredProcedureAjax(int structureId, int basketId,
          int productId, int identityId, string token)
        {
            Logger.Info(structureId, $"CalculProductByStoredProcedureAjax ({structureId}, {productId}, {identityId})");

            try
            {
                var productInDb = _productManager.GetById(structureId, productId);

                if (productInDb is not null && productInDb.TypeMontant == 2)
                {
                    //Appel de la procédure stockée qui retourne le montant
                    var resultMontant = _basketManager.GetCalculProductByStoredProcedure(structureId, basketId, identityId, productId);

                    return Ok(resultMontant);

                }
                return Ok(0);

            }
            catch (Exception ex)
            {
                return Problem(ex.Message);
            }
        }


        /// <summary>
        /// Obtient le panier en cours 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId">Id du panier</param>
        /// <param name="webUserId">Id du web user </param>
        /// <param name="langCode">Code de la langue (fr, de,...)</param>
        /// <returns></returns>
        private BasketDTO GetBasket(int structureId, int basketId, int webUserId, string langCode)
        {
            Logger.Trace(structureId, $"---- start GetBasket ---- {basketId}, {webUserId}, {langCode}");

            try
            {
                var listBask = _basketManager.GetAllBasketInfo(structureId, basketId, webUserId);
                if (listBask.Count == 1)
                {
                    var newBasket = listBask[0];

                    Logger.Debug(structureId, $"newBasket {newBasket.BasketId}");

                    if (newBasket.ListAllSeatsUnitSales()?.Count == 0 && newBasket.ListAbonnements?.Count == 0 && newBasket.ListProductCartesAdhesion?.Count == 0 &&
                        newBasket.ListSeatsAboMulti?.Count == 0 && newBasket.ListProduitsWT?.Count == 0)
                    {
                        //Si aucune ligne dans panier_entree alros on invalide le panier (etat=I)
                        bool isInvalidPanier = _basketManager.UpdateEtatPanier(structureId, basketId, "I") > 0;
                        Logger.Debug(structureId, $"newBasket isInvalidPanier {isInvalidPanier}");

                        if (isInvalidPanier)
                            newBasket.Etat = "I";
                    }

                    return newBasket;
                }
                else
                {
                    return new BasketDTO() // pas de panier, retourne panier vide, id = 0
                    {
                        BasketId = 0
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"GetBasket : {ex.Message} \n {ex.StackTrace}");
                throw;
            }
        }
    }


    static class Extensions
    {
        public static IList<T> Clone<T>(this IList<T> listToClone) where T : ICloneable
        {
            return listToClone.Select(item => (T)item.Clone()).ToList();
        }
    }

}
