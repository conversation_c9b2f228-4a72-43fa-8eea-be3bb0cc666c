﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Basket/style.1st4pyezey.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Basket\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1st4pyezey"},{"Name":"integrity","Value":"sha256-wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/Basket/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"27507"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Basket/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Basket\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"27507"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wZDp3Q5HZm83UJJhgzl4VhPwCgfuflQiKrtLaVfbL5k=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/catalog/style.g4aqg6g1za.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\catalog\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g4aqg6g1za"},{"Name":"integrity","Value":"sha256-wN02Zv8Pu3w2ox27gE3amYrrmis\u002B8KWuqzIAD5vscwg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/catalog/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11930"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wN02Zv8Pu3w2ox27gE3amYrrmis\u002B8KWuqzIAD5vscwg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/catalog/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\catalog\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wN02Zv8Pu3w2ox27gE3amYrrmis\u002B8KWuqzIAD5vscwg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11930"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022wN02Zv8Pu3w2ox27gE3amYrrmis\u002B8KWuqzIAD5vscwg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/CrossSelling/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\CrossSelling\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sVjlBfxg6iqeF0g7ULFoytSHYY\u002BzqSb4vtDHseikp94="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6011"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022sVjlBfxg6iqeF0g7ULFoytSHYY\u002BzqSb4vtDHseikp94=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/CrossSelling/style.vsciafzuvd.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\CrossSelling\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vsciafzuvd"},{"Name":"integrity","Value":"sha256-sVjlBfxg6iqeF0g7ULFoytSHYY\u002BzqSb4vtDHseikp94="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/CrossSelling/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6011"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022sVjlBfxg6iqeF0g7ULFoytSHYY\u002BzqSb4vtDHseikp94=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/brands.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\brands.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"18663"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/brands.min.ez2xkn8945.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\brands.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ez2xkn8945"},{"Name":"integrity","Value":"sha256-pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/fontawesome/brands.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"18663"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pmLi9Kfbkz7yfsgV/rMg2ESM//lt9lhrrxUJcCZR4Ew=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/fontawesome.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\fontawesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"80823"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/fontawesome.min.scp72qe8l0.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\fontawesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"scp72qe8l0"},{"Name":"integrity","Value":"sha256-5LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/fontawesome/fontawesome.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"80823"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225LmIRYJPm8LJW7MRYrvnkZLDY/LkMR7N1QBrcB2zwTc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/solid.min.51zpey82mz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\solid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"51zpey82mz"},{"Name":"integrity","Value":"sha256-mRMKBdbqofxkeOKFxu1cD\u002BaxDJzOeu37H7OErPSzNgw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/fontawesome/solid.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022mRMKBdbqofxkeOKFxu1cD\u002BaxDJzOeu37H7OErPSzNgw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/fontawesome/solid.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\fontawesome\solid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mRMKBdbqofxkeOKFxu1cD\u002BaxDJzOeu37H7OErPSzNgw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"572"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022mRMKBdbqofxkeOKFxu1cD\u002BaxDJzOeu37H7OErPSzNgw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/HomeModular/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\HomeModular\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22023"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/HomeModular/style.qkc7ff5z8n.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\HomeModular\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qkc7ff5z8n"},{"Name":"integrity","Value":"sha256-lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/HomeModular/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22023"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022lqmYz217Ovr1T4NftpZ1jy7THd89JhIntfuWbyI0JV0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Insurance/style.140muf1qxf.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Insurance\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"140muf1qxf"},{"Name":"integrity","Value":"sha256-EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/Insurance/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9480"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Insurance/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Insurance\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9480"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022EbRedkBMmE8ZWJxVYRh/nFlJNHTqZ7sQAxdaEfTgYCo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Product/ProductDetails/style.gv2otlqfd8.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Product\ProductDetails\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gv2otlqfd8"},{"Name":"integrity","Value":"sha256-jDUiX8Ca\u002B7parAAkqp1y\u002B7LAhzlvZ2YNH4k8Bw5uWMU="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/Product/ProductDetails/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2369"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022jDUiX8Ca\u002B7parAAkqp1y\u002B7LAhzlvZ2YNH4k8Bw5uWMU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Product/ProductDetails/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Product\ProductDetails\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jDUiX8Ca\u002B7parAAkqp1y\u002B7LAhzlvZ2YNH4k8Bw5uWMU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2369"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022jDUiX8Ca\u002B7parAAkqp1y\u002B7LAhzlvZ2YNH4k8Bw5uWMU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Product/style.1h25wggyzv.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Product\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1h25wggyzv"},{"Name":"integrity","Value":"sha256-O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/Product/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7928"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Product/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Product\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7928"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022O4QJ8AdLJdWnB1wTYNy/nSaYvVFNeevmdfOq4LIvNt8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Session/style.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Session\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fjj\u002Bqd75HfeNZSCNMmeJXJX0ftV/oLS/xEBO2DY8oJA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"29358"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022fjj\u002Bqd75HfeNZSCNMmeJXJX0ftV/oLS/xEBO2DY8oJA=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/Session/style.q6l5dos2rh.less">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\Session\style.less'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q6l5dos2rh"},{"Name":"integrity","Value":"sha256-fjj\u002Bqd75HfeNZSCNMmeJXJX0ftV/oLS/xEBO2DY8oJA="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/Session/style.less"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"29358"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022fjj\u002Bqd75HfeNZSCNMmeJXJX0ftV/oLS/xEBO2DY8oJA=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.izvpo0fq0c.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-brands-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"izvpo0fq0c"},{"Name":"integrity","Value":"sha256-Lvb93nBxk\u002BQ3widTdVyzL7iSHd5/9nQ5tY5tyjxaC\u002B8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"187448"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Lvb93nBxk\u002BQ3widTdVyzL7iSHd5/9nQ5tY5tyjxaC\u002B8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-brands-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Lvb93nBxk\u002BQ3widTdVyzL7iSHd5/9nQ5tY5tyjxaC\u002B8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"187448"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Lvb93nBxk\u002BQ3widTdVyzL7iSHd5/9nQ5tY5tyjxaC\u002B8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.w3gmvxomfo.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w3gmvxomfo"},{"Name":"integrity","Value":"sha256-9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"108000"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00229GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-brands-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-brands-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"108000"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00229GF0Iwi3eR/ubpaY4SEqpuj9HV5bQXlb/WjpOqARBz0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.i8qdkhure4.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-regular-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i8qdkhure4"},{"Name":"integrity","Value":"sha256-Et6he2iq\u002Bz9DyoFiigGzP\u002B1z2bvUNNvSvIUSvP8FNKY="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"63728"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Et6he2iq\u002Bz9DyoFiigGzP\u002B1z2bvUNNvSvIUSvP8FNKY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.tiuka3971e.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tiuka3971e"},{"Name":"integrity","Value":"sha256-e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24840"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-regular-400.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Et6he2iq\u002Bz9DyoFiigGzP\u002B1z2bvUNNvSvIUSvP8FNKY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"63728"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Et6he2iq\u002Bz9DyoFiigGzP\u002B1z2bvUNNvSvIUSvP8FNKY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-regular-400.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-regular-400.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24840"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022e6JMQTjEw8/mlKj8iUO4ziG5v7sU7cspC4ZU/Ko2XWs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.q5d2fvgdiy.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q5d2fvgdiy"},{"Name":"integrity","Value":"sha256-4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU\u002Bpg7kI/4I="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"149908"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00224sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU\u002Bpg7kI/4I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-solid-900.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Z6iAtLpSm5rMMD1ym1a0/XCG\u002ByLUJWYEELjFHheE9ik="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"394832"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Z6iAtLpSm5rMMD1ym1a0/XCG\u002ByLUJWYEELjFHheE9ik=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-solid-900.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU\u002Bpg7kI/4I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"149908"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u00224sXPVH4ujXShfQXFrZ8fWTylJkUuIoEkKU\u002Bpg7kI/4I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.xyf340uas6.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-solid-900.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xyf340uas6"},{"Name":"integrity","Value":"sha256-Z6iAtLpSm5rMMD1ym1a0/XCG\u002ByLUJWYEELjFHheE9ik="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-solid-900.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"394832"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Z6iAtLpSm5rMMD1ym1a0/XCG\u002ByLUJWYEELjFHheE9ik=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.0bf02qslzm.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-v4compatibility.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0bf02qslzm"},{"Name":"integrity","Value":"sha256-fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4536"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.1rckkh26l5.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-v4compatibility.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1rckkh26l5"},{"Name":"integrity","Value":"sha256-jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10172"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-v4compatibility.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10172"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022jZUA6EQBeetBh/M54LGi6EoPKYqJklDmrG0hi25aQH8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/css/webfonts/fa-v4compatibility.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\webfonts\fa-v4compatibility.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4536"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022fDd0Bbfnk1RUa93bfc1zvTh2DJXCvkTqHjCXWC2sZCw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/angle_button_white.hoal44qztg.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\angle_button_white.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hoal44qztg"},{"Name":"integrity","Value":"sha256-r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/img/angle_button_white.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"507"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/angle_button_white.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\angle_button_white.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"507"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022r2JoKYkMKNzJLTSxenuWYCbWxPHE/kNf5g8No8sNWqg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/sessionnotavailable.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\sessionnotavailable.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kIRR6icIjLjhvJTiRMZcxN9T74VR8\u002Bq5HvYJnmDI0iw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1072"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kIRR6icIjLjhvJTiRMZcxN9T74VR8\u002Bq5HvYJnmDI0iw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/sessionnotavailable.wwx3912tbx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\sessionnotavailable.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wwx3912tbx"},{"Name":"integrity","Value":"sha256-kIRR6icIjLjhvJTiRMZcxN9T74VR8\u002Bq5HvYJnmDI0iw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/img/sessionnotavailable.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1072"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kIRR6icIjLjhvJTiRMZcxN9T74VR8\u002Bq5HvYJnmDI0iw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/ticket-shape-bottom.kbtng4gjpy.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\ticket-shape-bottom.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kbtng4gjpy"},{"Name":"integrity","Value":"sha256-MFTmQZ3\u002BSjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/img/ticket-shape-bottom.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"566"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022MFTmQZ3\u002BSjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/img/ticket-shape-bottom.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\ticket-shape-bottom.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MFTmQZ3\u002BSjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"566"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022MFTmQZ3\u002BSjoxkogOYSPZlv9XwoOMKelq25lGi8uRBL8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/basket/basket.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\basket\basket.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PZYGac6rUDtt2heHoDvqEHd8NQL5/hSWd7LtD3cx8Xw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"52280"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PZYGac6rUDtt2heHoDvqEHd8NQL5/hSWd7LtD3cx8Xw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/basket/basket.t0aeoxrynh.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\basket\basket.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t0aeoxrynh"},{"Name":"integrity","Value":"sha256-PZYGac6rUDtt2heHoDvqEHd8NQL5/hSWd7LtD3cx8Xw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/basket/basket.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"52280"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PZYGac6rUDtt2heHoDvqEHd8NQL5/hSWd7LtD3cx8Xw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/bootstrap-spinner/bootstrap-input-spinner.91h9pnmz7i.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\bootstrap-spinner\bootstrap-input-spinner.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"91h9pnmz7i"},{"Name":"integrity","Value":"sha256-fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/bootstrap-spinner/bootstrap-input-spinner.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15520"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/bootstrap-spinner/bootstrap-input-spinner.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\bootstrap-spinner\bootstrap-input-spinner.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"15520"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fVO5zo9PWugD9Vb3dBr51QvMSKMebOXt/pOisrllAeA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/catalog/catalog.aybzvw0lo8.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\catalog\catalog.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aybzvw0lo8"},{"Name":"integrity","Value":"sha256-Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM\u002BQIx4="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/catalog/catalog.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4481"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM\u002BQIx4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/catalog/catalog.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\catalog\catalog.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM\u002BQIx4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4481"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Qma7/WillaNLoJoUur8qGNnh7gAM8zGqxBY7qM\u002BQIx4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/categprices/categprices.eg79ao16si.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\categprices\categprices.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eg79ao16si"},{"Name":"integrity","Value":"sha256-/sdO8jhyJ/Nql0tNPT\u002B5r6PTcOVTp6u2OHwrL1aWNaY="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/categprices/categprices.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1600"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/sdO8jhyJ/Nql0tNPT\u002B5r6PTcOVTp6u2OHwrL1aWNaY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/categprices/categprices.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\categprices\categprices.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/sdO8jhyJ/Nql0tNPT\u002B5r6PTcOVTp6u2OHwrL1aWNaY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1600"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/sdO8jhyJ/Nql0tNPT\u002B5r6PTcOVTp6u2OHwrL1aWNaY=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/commons.h0qjbqpakw.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\commons.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h0qjbqpakw"},{"Name":"integrity","Value":"sha256-j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ\u002BrtjV1GC0RM="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/commons.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21537"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ\u002BrtjV1GC0RM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/commons.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\commons.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ\u002BrtjV1GC0RM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21537"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022j1K4qu5XPBz/7O9E33nTK6pLAM1WTbkJ\u002BrtjV1GC0RM=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/crossSelling/crossSelling.arhin6lv8f.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\crossSelling\crossSelling.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"arhin6lv8f"},{"Name":"integrity","Value":"sha256-mnZLlcvkdD2Q1FdNxspr\u002BKAEC3ZJa1RUOp4K2\u002BrJMLE="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/crossSelling/crossSelling.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"484"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mnZLlcvkdD2Q1FdNxspr\u002BKAEC3ZJa1RUOp4K2\u002BrJMLE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/crossSelling/crossSelling.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\crossSelling\crossSelling.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mnZLlcvkdD2Q1FdNxspr\u002BKAEC3ZJa1RUOp4K2\u002BrJMLE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"484"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mnZLlcvkdD2Q1FdNxspr\u002BKAEC3ZJa1RUOp4K2\u002BrJMLE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/feedbook/feedbookForm.9simdimwy5.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\feedbook\feedbookForm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9simdimwy5"},{"Name":"integrity","Value":"sha256-mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/feedbook/feedbookForm.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16622"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/feedbook/feedbookForm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\feedbook\feedbookForm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16622"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022mR2mQwy7PcJSWz843dlUQUkFYFH7YV1C5gaJo2dGcts=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/homemodular/homemodular.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\homemodular\homemodular.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5035"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/homemodular/homemodular.pz7fifd5mo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\homemodular\homemodular.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pz7fifd5mo"},{"Name":"integrity","Value":"sha256-NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/homemodular/homemodular.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5035"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NdSYhMDbdPwakkf8FEDV8t1MGxmg5z1Y8uDFOAjjeAU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/insurance/insurance.5w9vrrajao.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\insurance\insurance.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5w9vrrajao"},{"Name":"integrity","Value":"sha256-KxXX2S8dPxTQSYJi\u002BijA9oDxCm7CUxC2AhCHrhozgqs="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/insurance/insurance.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3952"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KxXX2S8dPxTQSYJi\u002BijA9oDxCm7CUxC2AhCHrhozgqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/insurance/insurance.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\insurance\insurance.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KxXX2S8dPxTQSYJi\u002BijA9oDxCm7CUxC2AhCHrhozgqs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3952"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KxXX2S8dPxTQSYJi\u002BijA9oDxCm7CUxC2AhCHrhozgqs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/lessModify.h35nv9j0tn.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\lessModify.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h35nv9j0tn"},{"Name":"integrity","Value":"sha256-jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/lessModify.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4607"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/lessModify.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\lessModify.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4607"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jezxbrkIQNAYBKqvOtlRkYHmQxAB3UX3LZWzCHP/eaY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/product/product.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product\product.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"315"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/product/product.l813fa0qah.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product\product.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l813fa0qah"},{"Name":"integrity","Value":"sha256-BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/product/product.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"315"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022BYB9NsR90YJdC3jAS5IHl48SDD6dbRi3oBOR4Q2vqw8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/product/productDetails/productDetailsCADH.0x0wh8qap2.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product\productDetails\productDetailsCADH.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0x0wh8qap2"},{"Name":"integrity","Value":"sha256-XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/product/productDetails/productDetailsCADH.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22862"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/product/productDetails/productDetailsCADH.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\product\productDetails\productDetailsCADH.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22862"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XELZJWAO7ZzfEVCXsIHYqqxaMDhYh/HC1A6SnW8N2Vk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/seatsSelection/seatsSelection.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\seatsSelection\seatsSelection.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3100"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/seatsSelection/seatsSelection.y1y03gh8aq.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\seatsSelection\seatsSelection.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y1y03gh8aq"},{"Name":"integrity","Value":"sha256-Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/seatsSelection/seatsSelection.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3100"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Zt97dVqshMV7JwlMyqC0UZ7wU83v6jYpqysTVwxgM8g=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/pano.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\pano.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21094"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002274/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/pano.lf2ncmqb44.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\pano.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lf2ncmqb44"},{"Name":"integrity","Value":"sha256-74/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/session/pano.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21094"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002274/ZaziwSA6iAMaqMV5EfpmULLxiYzIhz1VwhT/v0hg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/seatplan.8w64e9o242.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\seatplan.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8w64e9o242"},{"Name":"integrity","Value":"sha256-GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz\u002Bz2hL8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/session/seatplan.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"34600"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz\u002Bz2hL8=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/seatplan.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\seatplan.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz\u002Bz2hL8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"34600"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022GHIUGqF0h8jZMJ7jfMUdVK4c8/8RiMCMR4qWz\u002Bz2hL8=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/session.abryv6ycit.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\session.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"abryv6ycit"},{"Name":"integrity","Value":"sha256-xjXUmYF37DdwUMNu1tNLa4mYbtNphUBW3Bfd/0Vh/TY="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/session/session.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"109628"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xjXUmYF37DdwUMNu1tNLa4mYbtNphUBW3Bfd/0Vh/TY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/session/session.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\session\session.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xjXUmYF37DdwUMNu1tNLa4mYbtNphUBW3Bfd/0Vh/TY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"109628"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xjXUmYF37DdwUMNu1tNLa4mYbtNphUBW3Bfd/0Vh/TY=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/site.6b9nfc4n3r.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6b9nfc4n3r"},{"Name":"integrity","Value":"sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/site.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"230"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/site.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"230"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224q1jwFhaPaZgr8WAUSrux6hAuh0XDg9kPS3xIVq36I0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/tunnel/tunnel.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\tunnel\tunnel.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MODq8\u002BxYTX9x74jt\u002BP1qSk4v7tqc6Y9f7c2v/\u002BevnFg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4691"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022MODq8\u002BxYTX9x74jt\u002BP1qSk4v7tqc6Y9f7c2v/\u002BevnFg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/tunnel/tunnel.sp16istu3z.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\tunnel\tunnel.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sp16istu3z"},{"Name":"integrity","Value":"sha256-MODq8\u002BxYTX9x74jt\u002BP1qSk4v7tqc6Y9f7c2v/\u002BevnFg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/tunnel/tunnel.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4691"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022MODq8\u002BxYTX9x74jt\u002BP1qSk4v7tqc6Y9f7c2v/\u002BevnFg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/widget.j8suanul2b.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\widget.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j8suanul2b"},{"Name":"integrity","Value":"sha256-je/HkzXyPp\u002BNG2gRlQmlVmIToJ04YklPcqlZ99BNEMI="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/js/widget.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"31950"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022je/HkzXyPp\u002BNG2gRlQmlVmIToJ04YklPcqlZ99BNEMI=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/js/widget.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\widget.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-je/HkzXyPp\u002BNG2gRlQmlVmIToJ04YklPcqlZ99BNEMI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"31950"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022je/HkzXyPp\u002BNG2gRlQmlVmIToJ04YklPcqlZ99BNEMI=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.9n0ta5ieki.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9n0ta5ieki"},{"Name":"integrity","Value":"sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"68266"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"68266"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.css.0i1dcxd824.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0i1dcxd824"},{"Name":"integrity","Value":"sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q\u002BMs="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"151749"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00222MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q\u002BMs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q\u002BMs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"151749"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00222MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q\u002BMs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vdSFRAWr6LTognRmxyi6QlSO5O\u002BMC\u002BVGyMbziTrBmBQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48494"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022vdSFRAWr6LTognRmxyi6QlSO5O\u002BMC\u002BVGyMbziTrBmBQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.css.jzb7jyrjvs.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jzb7jyrjvs"},{"Name":"integrity","Value":"sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"108539"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"108539"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.vxs71z90fw.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vxs71z90fw"},{"Name":"integrity","Value":"sha256-vdSFRAWr6LTognRmxyi6QlSO5O\u002BMC\u002BVGyMbziTrBmBQ="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-grid.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48494"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022vdSFRAWr6LTognRmxyi6QlSO5O\u002BMC\u002BVGyMbziTrBmBQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6\u002BbHmo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5227"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6\u002BbHmo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"76483"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.css.tgg2bl5mrw.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tgg2bl5mrw"},{"Name":"integrity","Value":"sha256-3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"76483"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.brwg1hntyu.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"brwg1hntyu"},{"Name":"integrity","Value":"sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4028"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00221z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4028"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00221z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.css.gf2dxac9qe.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gf2dxac9qe"},{"Name":"integrity","Value":"sha256-dIm3VZXztwbIlhOzVt\u002Bggg5Dvhp28MJQGJoweOH9cAE="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"32461"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022dIm3VZXztwbIlhOzVt\u002Bggg5Dvhp28MJQGJoweOH9cAE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dIm3VZXztwbIlhOzVt\u002Bggg5Dvhp28MJQGJoweOH9cAE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"32461"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022dIm3VZXztwbIlhOzVt\u002Bggg5Dvhp28MJQGJoweOH9cAE=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.nynt4yc5xr.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nynt4yc5xr"},{"Name":"integrity","Value":"sha256-cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6\u002BbHmo="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap-reboot.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5227"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6\u002BbHmo=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eEx7gvq\u002BuEM0o4kUBiy/\u002BMxl6rHH9NQ9UzRBWHe9mXg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"202385"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022eEx7gvq\u002BuEM0o4kUBiy/\u002BMxl6rHH9NQ9UzRBWHe9mXg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.css.k9n1kkbua6.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k9n1kkbua6"},{"Name":"integrity","Value":"sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"492048"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"492048"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.gawgt6fljy.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gawgt6fljy"},{"Name":"integrity","Value":"sha256-eEx7gvq\u002BuEM0o4kUBiy/\u002BMxl6rHH9NQ9UzRBWHe9mXg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"202385"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022eEx7gvq\u002BuEM0o4kUBiy/\u002BMxl6rHH9NQ9UzRBWHe9mXg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm\u002B224CacZs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"155764"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm\u002B224CacZs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.css.kao5znno1s.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kao5znno1s"},{"Name":"integrity","Value":"sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"625953"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"625953"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.etnb7xlipe.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"etnb7xlipe"},{"Name":"integrity","Value":"sha256-rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm\u002B224CacZs="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/css/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"155764"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm\u002B224CacZs=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.48vr37mrsy.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"48vr37mrsy"},{"Name":"integrity","Value":"sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7\u002BDsnwGNwA="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"136072"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7\u002BDsnwGNwA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"229924"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3UpdqvoTc6M2sug8WtFhr/m3tg\u002B4zLMgoMgjqpn5n1I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"402249"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223UpdqvoTc6M2sug8WtFhr/m3tg\u002B4zLMgoMgjqpn5n1I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.js.pxamm17y9e.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pxamm17y9e"},{"Name":"integrity","Value":"sha256-3UpdqvoTc6M2sug8WtFhr/m3tg\u002B4zLMgoMgjqpn5n1I="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"402249"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00223UpdqvoTc6M2sug8WtFhr/m3tg\u002B4zLMgoMgjqpn5n1I=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.hjuzisly30.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hjuzisly30"},{"Name":"integrity","Value":"sha256-XZfkOGd6FuhF88h5GgEmRIpXbm\u002BhBkFo74yYDPY5rbw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"78641"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XZfkOGd6FuhF88h5GgEmRIpXbm\u002BhBkFo74yYDPY5rbw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XZfkOGd6FuhF88h5GgEmRIpXbm\u002BhBkFo74yYDPY5rbw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"78641"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XZfkOGd6FuhF88h5GgEmRIpXbm\u002BhBkFo74yYDPY5rbw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8i3JQdKYQQcJzmbkwhwY\u002B1XPe7Utf1LdBnYZCvNmKWc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"311949"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00228i3JQdKYQQcJzmbkwhwY\u002B1XPe7Utf1LdBnYZCvNmKWc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.js.u9xms436mi.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u9xms436mi"},{"Name":"integrity","Value":"sha256-8i3JQdKYQQcJzmbkwhwY\u002B1XPe7Utf1LdBnYZCvNmKWc="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"311949"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00228i3JQdKYQQcJzmbkwhwY\u002B1XPe7Utf1LdBnYZCvNmKWc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.u0biprgly9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u0biprgly9"},{"Name":"integrity","Value":"sha256-srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.bundle.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"229924"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7\u002BDsnwGNwA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"136072"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7\u002BDsnwGNwA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"250568"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.js.vaswmcjbo4.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vaswmcjbo4"},{"Name":"integrity","Value":"sha256-R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"250568"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"58078"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vMfBbEXmojM9AaHrIyKSo\u002B20n5JM7KMyJkBCfL4pgL4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"190253"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022vMfBbEXmojM9AaHrIyKSo\u002B20n5JM7KMyJkBCfL4pgL4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.js.sgi57dik4g.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sgi57dik4g"},{"Name":"integrity","Value":"sha256-vMfBbEXmojM9AaHrIyKSo\u002B20n5JM7KMyJkBCfL4pgL4="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"190253"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022vMfBbEXmojM9AaHrIyKSo\u002B20n5JM7KMyJkBCfL4pgL4=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.zu238p5lxg.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zu238p5lxg"},{"Name":"integrity","Value":"sha256-O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/dist/js/bootstrap.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"58078"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/LICENSE">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE\u002Bao7bBq0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE\u002Bao7bBq0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/bootstrap/LICENSE.weyt030wr8">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"weyt030wr8"},{"Name":"integrity","Value":"sha256-iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE\u002Bao7bBq0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/bootstrap/LICENSE"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE\u002Bao7bBq0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.0td7jq9nxb.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0td7jq9nxb"},{"Name":"integrity","Value":"sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19366"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19366"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5867"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.v12ed9ioy0.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v12ed9ioy0"},{"Name":"integrity","Value":"sha256-4xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5867"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224xJOkIkaeBplN5tlcvxnCbBz34o5/kzpXcpjNkonVos=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"587"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/LICENSE.wqejeusuyq.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wqejeusuyq"},{"Name":"integrity","Value":"sha256-aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation-unobtrusive/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"587"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"43184"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC\u002Bw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"18467"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00222F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC\u002Bw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.min.sjab29p8z5.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sjab29p8z5"},{"Name":"integrity","Value":"sha256-2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC\u002Bw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"18467"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00222F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC\u002Bw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.wus95c49fh.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wus95c49fh"},{"Name":"integrity","Value":"sha256-qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/additional-methods.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"43184"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.jdpnbaa6vo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jdpnbaa6vo"},{"Name":"integrity","Value":"sha256-yazfaIh2SXu8rPenyD2f36pKgrkv5XT\u002BDQCDpZ/eDao="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48676"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022yazfaIh2SXu8rPenyD2f36pKgrkv5XT\u002BDQCDpZ/eDao=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yazfaIh2SXu8rPenyD2f36pKgrkv5XT\u002BDQCDpZ/eDao="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48676"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022yazfaIh2SXu8rPenyD2f36pKgrkv5XT\u002BDQCDpZ/eDao=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.min.59qolqi738.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"59qolqi738"},{"Name":"integrity","Value":"sha256-F6h55Qw6sweK\u002Bt7SiOJX\u002B2bpSAa3b/fnlrVCJvmEj1A="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23261"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022F6h55Qw6sweK\u002Bt7SiOJX\u002B2bpSAa3b/fnlrVCJvmEj1A=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/dist/jquery.validate.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-F6h55Qw6sweK\u002Bt7SiOJX\u002B2bpSAa3b/fnlrVCJvmEj1A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23261"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022F6h55Qw6sweK\u002Bt7SiOJX\u002B2bpSAa3b/fnlrVCJvmEj1A=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/LICENSE.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery-validation/LICENSE.x0q3zqp4vz.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x0q3zqp4vz"},{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery-validation/LICENSE.md"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"287630"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.5trh6b1mit.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5trh6b1mit"},{"Name":"integrity","Value":"sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"89476"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.dsw5v3fbc5.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dsw5v3fbc5"},{"Name":"integrity","Value":"sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137974"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"89476"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.min.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"137974"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.vwa26bmbsk.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vwa26bmbsk"},{"Name":"integrity","Value":"sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery/dist/jquery.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"287630"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/LICENSE.afgyafcsqt.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"afgyafcsqt"},{"Name":"integrity","Value":"sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/lib/jquery/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1641"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/lib/jquery/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1641"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/testingCallOffersLocal.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\testingCallOffersLocal.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G626HHRkuN\u002Bsz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2520"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G626HHRkuN\u002Bsz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/testingCallOffersLocal.pn1mdxt63j.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\testingCallOffersLocal.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pn1mdxt63j"},{"Name":"integrity","Value":"sha256-G626HHRkuN\u002Bsz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/testingCallOffersLocal.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2520"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G626HHRkuN\u002Bsz4W6KLjR2vJ7awQ/6DPm28dyv2WPhXU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.3d1vw2wx03.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteexterne.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3d1vw2wx03"},{"Name":"integrity","Value":"sha256-WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB\u002BwYMg="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"457"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB\u002BwYMg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.6vn0p36f17.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteexterne.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6vn0p36f17"},{"Name":"integrity","Value":"sha256-LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA\u002B3rVoU="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2794"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA\u002B3rVoU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteexterne.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA\u002B3rVoU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2794"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022LsKBieYiuynLklR5JENWJHd1yUnJB/dplVaLA\u002B3rVoU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterne.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\WebSiteExterne.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xLNUqcY/BBhgUgY7E9lWbBpR6tPiIy7SKmgWHKFMPUI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11138"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022xLNUqcY/BBhgUgY7E9lWbBpR6tPiIy7SKmgWHKFMPUI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteexterne.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteexterne.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB\u002BwYMg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"457"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WT/c2yXRuMSelcknd1WVhU2qkcENa/4nYdbHeB\u002BwYMg=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterne.k0vj4jmmpq.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\WebSiteExterne.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k0vj4jmmpq"},{"Name":"integrity","Value":"sha256-xLNUqcY/BBhgUgY7E9lWbBpR6tPiIy7SKmgWHKFMPUI="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterne.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11138"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022xLNUqcY/BBhgUgY7E9lWbBpR6tPiIy7SKmgWHKFMPUI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterneCallLocal.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\WebSiteExterneCallLocal.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LEUbPox0uTSpJ4mh6t34apQDSVY16z\u002BnLvxwU5koeBI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11720"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022LEUbPox0uTSpJ4mh6t34apQDSVY16z\u002BnLvxwU5koeBI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterneCallLocal.sizgbyf03s.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\WebSiteExterneCallLocal.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sizgbyf03s"},{"Name":"integrity","Value":"sha256-LEUbPox0uTSpJ4mh6t34apQDSVY16z\u002BnLvxwU5koeBI="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/WebSiteExterneCallLocal.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11720"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022LEUbPox0uTSpJ4mh6t34apQDSVY16z\u002BnLvxwU5koeBI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 19 Dec 2024 13:50:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteExterneTunnel.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteExterneTunnel.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12901"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteExterneTunnel.rbsndwsa29.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\websiteexterneDemo\websiteExterneTunnel.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rbsndwsa29"},{"Name":"integrity","Value":"sha256-J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc="},{"Name":"label","Value":"_content/Core.Themis.Widgets.Offers/websiteexterneDemo/websiteExterneTunnel.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12901"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022J9yMsklk0ol2gOfJzvc8aDW9K4tTkjS9n8ZMZWPCnZc=\u0022"},{"Name":"Last-Modified","Value":"Tue, 24 Jun 2025 07:37:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>